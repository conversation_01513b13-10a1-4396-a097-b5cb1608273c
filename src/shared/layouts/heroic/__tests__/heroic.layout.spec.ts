import { shallowMount } from '@vue/test-utils';

import HeroicLayout from '..';

describe('HeroicLayout', () => {
  test('render', () => {
    const wrapper = shallowMount(HeroicLayout, {
      slots: {
        hero: 'Hero',
        default: 'Body',
      },
    });
    expect(wrapper).toMatchSnapshot();
  });

  test('slot: hero', () => {
    const wrapper = shallowMount(HeroicLayout, {
      slots: {
        hero: 'Hero',
      },
    });
    expect(wrapper).toMatchSnapshot();
  });

  test('slot: default', () => {
    const wrapper = shallowMount(HeroicLayout, {
      slots: {
        default: 'Body',
      },
    });
    expect(wrapper).toMatchSnapshot();
  });
});
