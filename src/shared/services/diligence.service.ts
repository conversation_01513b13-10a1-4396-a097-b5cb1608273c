import { isEmpty, minBy, omit } from 'lodash';

import { retry, type HttpClient } from '@/utils/http-client';
import { mergeRelations, processRelationRoles } from '@/utils/helper/data-parser';
import { isJSONString } from '@/utils/data-type/is-json-string';

export const DILIGENCE_BASE = '/diligence';
export const DILIGENCE_SCAN_RISK = `${DILIGENCE_BASE}/scanRisk`;
export const DILIGENCE_BACKGROUND_SCAN_RISK = `${DILIGENCE_BASE}/backgroud/`;
export const DILIGENCE_SEARCH = `${DILIGENCE_BASE}/search`;
export const DILIGENCE_DETAIL = `${DILIGENCE_BASE}/details`;
export const DILIGENCE_RATING = `${DILIGENCE_BASE}/thirdPartyRating`;
export const DILIGENCE_CRET = `${DILIGENCE_BASE}/cretList`;
export const OUT_BLACKLIST_INFO = `${DILIGENCE_BASE}/outerblacklist`;
export const INTERESTCONFLIICT_INFO = `${DILIGENCE_BASE}/interestConflict`;
export const PARTNER_INFO = `${DILIGENCE_BASE}/partnerList`;
export const INNER_BLACKLIST_INFO = `${DILIGENCE_BASE}/innerblacklist`;
export const DILIGENCE_LAYOUT = `/settings`;
export const SNAPSHOT = `${DILIGENCE_BASE}/snapshot`;
export const SNAPSHOT_REFRESH = `${DILIGENCE_BASE}/refresh`;

// 作用：兼容老数据结构，目前数据结构有两套，为了兼容老结构，做了一定转换
export const getTranslateItem = (item) => {
  return {
    Id: item.id || item.Id || Math.random().toString(36).slice(2),
    stockPercent: item.stockpercent ? `${item.stockpercent}%` : undefined, // 持股比例
    personId: item.personKeyno, // 人员编号
    personName: item.personName, // 人员名称
    relatedCompanyId: item.companyKeynoRelated, // 关联企业编号
    relatedCompanyName: item.companyNameRelated, // 关联企业名称
    companyId: item.companyKeynoDD, // 企业编号
    companyName: item.companyNameDD, // 企业名称
    job: item.roleDD,
    relatedJob: item.roleRelated,
    ...item,
  };
};

// 给图谱数据转换使用
export const getTranslatePathItem = (item) => {
  return {
    ...item,
    Id: item.id || item.Id || Math.random().toString(36).slice(2),
    stockPercent: item.stockpercent ? `${item.stockpercent}%` : undefined, // 持股比例
    personId: item.personId || item.personKeyno || item.keyNo, // 人员编号
    personName: item.personName || item.name, // 人员名称
    relatedCompanyId: item.companyKeynoRelated || item.companyId, // 关联企业编号
    relatedCompanyName: item.companyNameRelated || item.companyName, // 关联企业名称
    companyId: item.companyKeynoDD || item.sourceCompanyId, // 企业编号
    companyName: item.companyNameDD || item.sourceCompanyName, // 企业名称
    job: item.roleDD || item.relationType,
    relatedJob: item.roleRelated || item.relationType,
  };
};
export const getTranslateDetail = (data) => {
  const { Result = [], ...rest } = data;
  return {
    ...rest,
    Result: Result.map(getTranslateItem),
  };
};

export const getTransV2Detail = (data) => {
  const { Result = [], ...rest } = data;
  Result.forEach((item) => {
    const { companyNameRelated, companyKeynoRelated, relationPaths } = item;
    item.endCompanyKeyno = companyKeynoRelated;
    item.endCompanyName = companyNameRelated;
    relationPaths.forEach((paths) => {
      for (let i = 1; i < paths.length - 1; i += 2) {
        if (i % 2 === 1) {
          const { endid, startid } = paths[i];
          // 边关系，判断上一个节点是否为人 node
          if (paths[i - 1]['Person.name']) {
            paths[i].direction = 1;
            if (!startid?.startsWith('p')) {
              paths[i].endid = startid;
              paths[i].startid = endid;
            }
          }
          if (paths[i + 1]['Person.name']) {
            paths[i].direction = -1;
            if (!endid?.startsWith('p')) {
              paths[i].endid = startid;
              paths[i].startid = endid;
            }
          }
        }
      }
    });
    item.relations = relationPaths.map(processRelationRoles);
  });
  const relations = mergeRelations(Result).map((_resData) => {
    if (_resData.relations2) {
      _resData.relations = _resData.relations2;
    } else {
      // 找出最小长度，并筛选出来最小长度的路径
      const minRelations = minBy(_resData.relations, 'length');
      _resData.relations = _resData.relations.filter((rel) => rel.length === minRelations?.length); // 取合并后的最短路径
    }
    return _resData;
  });
  return {
    Result: relations,
    ...rest,
  };
};

const getJudicialCaseData = ({ Result, Paging }) => {
  if (isEmpty(Result)) return [];
  return Result.flatMap((item, index) => {
    const { PageIndex, PageSize } = Paging || {};
    const actualIndex = (PageIndex - 1) * PageSize + index + 1;
    if (isEmpty(item.TrailRoundDetails)) {
      return {
        ...item,
        index: actualIndex,
        caseId: item.Id,
        CaseRoleSearch: isJSONString(item.CaseRoleSearch) ? JSON.parse(item.CaseRoleSearch) : item.CaseRoleSearch,
      };
    }
    return item.TrailRoundDetails?.map((sub, index, arr) => ({
      ...item,
      TrailRoundDetail: sub,
      caseId: item.Id,
      CaseRoleSearch: isJSONString(item.CaseRoleSearch) ? JSON.parse(item.CaseRoleSearch) : item.CaseRoleSearch,
      index: actualIndex,
      Id: `${item.Id}-${index}`,
      attrs: { rowSpan: index === 0 ? arr.length : 0 },
    }));
  });
};

export const createService = (httpClient: HttpClient) => ({
  scanRisk(data = {} as any): Promise<any> {
    return httpClient.post('/diligence/scanRisk', data);
  },
  scanRiskDetail(data): Promise<any> {
    return httpClient.post(`/diligence/getDiligenceDetails`, data);
  },
  // 维度详情接口
  detail(key, data): Promise<any> {
    return retry(() => httpClient.post(`/diligence/getDimensionDetails`, data), {
      maxRetries: 3,
      delay: 1000,
      condition: (res) => res?.data?.status === 'FAILED',
    }).then((res) => {
      if (key === 'JudicialCase') {
        res.Result = getJudicialCaseData(res);
      }
      // 债券接口单独用了多个数据源，需要单独做处理
      if (key === 'BondDefaults') {
        res.Result = res.Result.map((t) => ({
          BondShortName: t.Title, // 债券名称
          BondTypeName: t.Casereasontype, // 债券类型
          DefaultStatusDesc: t.ExecuteStatus, // 违约状态
          FirstDefaultDate: t.PublishDate, // 首次违约日期
          AccuOverdueCapital: t.Amount, // 累计违约本金(亿元)
          AccuOverdueInterest: t.Amount2, // 累计违约利息(亿元)
          MaturityDate: t.LianDate, // 到期日期
          ...t,
        }));
      }
      // [破产重整]接口单独用了多个数据源，需要单独做处理
      if (key === 'Bankruptcy') {
        return {
          ...res,
          // 新数据源格式兼容：老数据源 || 新数据源
          Result: res.Result.map((item) => ({
            ...item,
            CaseReasonType: item.CaseReasonType || item.CaseType,
            SubjectInfo: item.SubjectInfo || item.RespondentNameAndKeyNo,
            NameAndKeyNo: item.NameAndKeyNo || item.ApplicantNameAndKeyNo,
            Court: item.Court || item.CourtName,
            RiskDate: item.PublishDate || item.RiskDate,
          })),
        };
      }
      // 与内部黑名单企业存在疑似关联关系,与第三方列表企业存在交叉重叠疑似关联 返回的图谱数据要额外处理
      if (['CustomerSuspectedRelation', 'BlacklistSuspectedRelation'].includes(key)) {
        return res;
      }
      return getTranslateDetail(res);
    });
  },
  getRelatedCompanyDetails(data): Promise<any> {
    const { key } = data;
    return httpClient.post('/diligence/getRelatedCompanyDetails', data).then((res) => {
      if (key === 'CompanyCredit') {
        return {
          ...res,
          Result: res.Result.map((item) => ({
            ...item,
            AddDate: item.publishdate,
            AddOffice: item.court,
            AddReason: item.casereason,
          })),
        };
      }
      return res;
    });
  },
  innerBlackList(data): Promise<any> {
    return httpClient.post(INNER_BLACKLIST_INFO, data);
  },
  outerBlackList(data): Promise<any> {
    return httpClient.post(OUT_BLACKLIST_INFO, data);
  },
  getLayout(): Promise<any> {
    return httpClient.get(DILIGENCE_LAYOUT);
  },
  updateLayout(data): Promise<any> {
    return httpClient.post(DILIGENCE_LAYOUT, data);
  },
  getThirdPartyRating(data): Promise<any> {
    return httpClient.post(DILIGENCE_RATING, data);
  },
  getCret(data): Promise<any> {
    return httpClient.post(DILIGENCE_CRET, data);
  },
  getInterestConflict(data): Promise<any> {
    return httpClient.post(INTERESTCONFLIICT_INFO, data);
  },
  getPartnerList(data): Promise<any> {
    return httpClient.post(PARTNER_INFO, data);
  },
  search(data): Promise<any> {
    return httpClient.post('/diligence/getDiligenceList', data);
  },
  getAggSearch(data): Promise<any> {
    return httpClient.post('/diligence/getAggSearch', data);
  },
  snapshot(data): Promise<any> {
    return httpClient.post(SNAPSHOT, data);
  },
  refreshSnapshot(data): Promise<any> {
    return httpClient.post(SNAPSHOT_REFRESH, data);
  },
  export(data): Promise<any> {
    return httpClient.post('/batch/export/diligence_record', data);
  },
  // 人员核实
  verifyPerson(data): Promise<any> {
    return httpClient.post('/diligence/update/person/verify', data);
  },

  // 排查结果修改
  editDetail(data): Promise<any> {
    return httpClient.post(`/diligence/${data.id}/remark`, omit(data, ['id']));
  },

  // 排查结果修改历史
  getEditDetailHistory(data): Promise<any> {
    return httpClient.post(`/diligence/${data.id}/remark/list`, omit(data, ['id']));
  },

  // 生成报告
  generateReport(id, options?): Promise<any> {
    return httpClient.post(`/batch/export/diligence_pdf/${id}`, undefined, options);
  },

  // 轮询获取生成报告的 BatchId
  pollingGenerateReport(diligenceId, delay = 2000): Promise<{ batchId: number }> {
    return new Promise((resolve, reject) => {
      const timer = setInterval(async () => {
        try {
          const res = await this.generateReport(diligenceId, {
            skipInterceptor: ['error'],
          });
          if (res.batchId) {
            clearInterval(timer);
            resolve(res);
          }
        } catch (error: any) {
          //  轮询条件: 500202 数据处理中，请稍后再试
          if (error?.response?.data?.code !== 500202) {
            clearInterval(timer);
            reject(error);
          }
        }
      }, delay);
    });
  },

  // 获取报告
  getReport(id): Promise<any> {
    return httpClient.get(`/batch/detail`, { params: { id } });
  },

  // 批量任务中止
  discontinue(data): Promise<any> {
    return httpClient.post('/batch/discontinue', data);
  },
});
