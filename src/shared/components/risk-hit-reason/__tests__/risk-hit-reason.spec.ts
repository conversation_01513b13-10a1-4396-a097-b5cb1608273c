import { shallowMount } from '@vue/test-utils';

import RiskHitReason from '..';

vi.mock('@/apps/risk-model/pages/risk-model-detail/widgets/model-dimenison-settings/widgets/strategy-presentation', () => ({
  default: {
    template: '<div>StrategyPresentation</div>',
  },
}));

describe('RiskHitReason', () => {
  it('命中规则为空时，不渲染任何内容', () => {
    const hitDetails = {};
    const dimensionStrategies = [];
    const wrapper = shallowMount(RiskHitReason, {
      propsData: {
        hitDetails,
        dimensionStrategies,
      },
    });
    expect(wrapper.findAll('.record').length).toBe(0);
  });

  it('命中规则包含多个策略类型 (must, should, must_not)', () => {
    const hitDetails = {
      must: [{ strategyId: '1', value: 'test1' }],
      should: [{ strategyId: '2', value: 'test2' }],
      must_not: [{ strategyId: '3', value: 'test3' }],
    };
    const dimensionStrategies = [
      {
        dimensionStrategyId: '1',
        dimensionHitStrategyEntity: {
          strategyName: 'Strategy 1',
          strategyFields: [{ dimensionFieldId: '1', accessScope: 2, dimensionField: { fieldOrder: 1 } }],
        },
      },
      {
        dimensionStrategyId: '2',
        dimensionHitStrategyEntity: {
          strategyName: 'Strategy 2',
          strategyFields: [{ dimensionFieldId: '2', accessScope: 2, dimensionField: { fieldOrder: 2 } }],
        },
      },
      {
        dimensionStrategyId: '3',
        dimensionHitStrategyEntity: {
          strategyName: 'Strategy 3',
          strategyFields: [{ dimensionFieldId: '3', accessScope: 2, dimensionField: { fieldOrder: 3 } }],
        },
      },
    ];
    const wrapper = shallowMount(RiskHitReason, {
      propsData: {
        hitDetails,
        dimensionStrategies,
      },
      stubs: {
        StrategyPresentation: true,
      },
    });
    expect(wrapper.findAll('[data-testid="risk-hit-reason"]').length).toBe(3);
    expect(wrapper.text()).toContain('Strategy 1');
    expect(wrapper.text()).toContain('Strategy 2');
    expect(wrapper.text()).toContain('Strategy 3');
  });
});
