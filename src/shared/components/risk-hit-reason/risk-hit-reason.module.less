.container {
  max-width: 584px;

  .group {
    display: flex;
    flex-direction: column;
    gap: 8px;

    .content {
      display: flex;
      flex-direction: column;
      gap: 10px;
    }
  }

  .record {
    .header {
      height: 50px;
      line-height: 50px;
      font-weight: 700;
      font-size: 15px;
      position: relative;

      &::after {
        content: "";
        position: absolute;
        left: 0;
        right: 0;
        bottom: 0;
        height: 1px;
        background-color: #ebebeb;
      }
    }
  }

  .query {
    padding: 15px 0;
    
    .dimension + .dimension{
      margin-top: 20px;
    }

    .name {
      display: flex;
      gap: 5px;
      font-weight: 500;
      margin-bottom: 8px;
      align-items: center;

      .index{
        display: flex;
        align-items: center;
        justify-content: space-around;
        width: 16px;
        height: 16px;
        border-radius: 16px;
        background: #128BED;
        color: #fff;
        transform: translateY(-1px);
      }
    }

    .body {
      flex: 1;
      display: inline-flex;
      flex-wrap: wrap;
      gap: 5px;
    }
  }
}