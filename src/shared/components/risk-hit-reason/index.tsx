import { isEmpty, sortBy } from 'lodash';
import { defineComponent, VNode } from 'vue';

// eslint-disable-next-line max-len
import StrategyPresentation from '@/apps/risk-model/pages/risk-model-detail/widgets/model-dimenison-settings/widgets/strategy-presentation';

import styles from './risk-hit-reason.module.less';

const createQuery = (values: Record<string, any>[], dimensionStrategies: Record<string, any>[], showTitle = true) => {
  const normalizedData = values.reduce((acc, item) => {
    const condition = dimensionStrategies.find((ds) => ds.dimensionStrategyId === item.strategyId);
    if (!condition) {
      return acc;
    }
    // 需要展示的策略字段
    const validFields = sortBy(
      condition.dimensionHitStrategyEntity.strategyFields.filter((sf) => sf.accessScope !== 1),
      'dimensionField.fieldOrder'
    );
    // 当只有一个策略且没有策略字段时，不显示维度名称
    // 赋值保存，展示的时候不用重新计算
    item.activeFields = validFields;
    item.dimensionDefName = condition?.dimensionHitStrategyEntity?.strategyName;
    if (condition.dimensionHitStrategies?.length === 1 && !validFields?.length) {
      return acc;
    }
    return acc.concat([item]);
  }, []);

  const showIndex = normalizedData.length > 1; // 命中总数

  const hitDimensionDetails = normalizedData.map((item, index) => {
    const q = item.activeFields.map((sf) => <StrategyPresentation key={sf.dimensionFieldId} strategyField={sf} />);

    return (
      <div class={styles.dimension}>
        <div class={styles.name}>
          {showIndex ? <span class={styles.index}>{index + 1}</span> : null}
          <span>
            {item.dimensionDefName}
            {q?.length ? ':' : ''}
          </span>
        </div>
        {q?.length ? <div class={styles.body}>{q}</div> : null}
      </div>
    );
  });
  return (
    <div data-testid="risk-hit-reason" class={styles.record}>
      <header v-show={showTitle} class={styles.header}>
        命中规则
      </header>
      <div class={styles.query}>{hitDimensionDetails}</div>
    </div>
  );
};

const STRATEGY = {
  must: (values: Record<string, any>[], dimensionStrategies: Record<string, any>[], showTitle?: boolean) => {
    return createQuery(values, dimensionStrategies, showTitle);
  },
  should: (values: Record<string, any>[], dimensionStrategies: Record<string, any>[], showTitle?: boolean) => {
    return createQuery(values, dimensionStrategies, showTitle);
  },
  must_not: (values: Record<string, any>[], dimensionStrategies: Record<string, any>[], showTitle?: boolean) => {
    return createQuery(values, dimensionStrategies, showTitle);
  },
};

const RiskHitReason = defineComponent({
  name: 'RiskHitReason',
  props: {
    hitDetails: {
      type: Object,
      required: true,
    },
    dimensionStrategies: {
      type: Array,
      required: true,
    },
    showTitle: {
      type: Boolean,
      default: true,
    },
  },
  render() {
    return (
      <div class={styles.container}>
        {Object.entries(this.hitDetails).reduce<VNode[]>((acc, [key, value]) => {
          if (!STRATEGY[key] || isEmpty(value)) {
            return acc;
          }
          const query = STRATEGY[key](value, this.dimensionStrategies, this.showTitle);
          return acc.concat([
            <div key={key} class={styles.group} data-hit-type={key}>
              <div class={styles.content}>{query}</div>
            </div>,
          ]);
        }, [])}
      </div>
    );
  },
});

export default RiskHitReason;
