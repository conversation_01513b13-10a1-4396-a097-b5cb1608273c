import { defineComponent } from 'vue';
import { Popover } from 'ant-design-vue';
import { omit } from 'lodash';

import QIcon from '@/components/global/q-icon';
import RiskHitReason from '@/shared/components/risk-hit-reason';

import styles from './risk-hit-reason-wrapper.module.less';

type DetailParams = {
  showTitle: boolean;
  hitDetails: Record<string, any>[];
  dimensionStrategies: Record<string, any>[];
  [key: string]: any;
};
const RiskHitReasonWrapper = defineComponent({
  functional: true,
  render(h, { props }) {
    const { placement, showTitle, hitDetails, dimensionStrategies, ...rest } = props as DetailParams;
    return (
      <Popover placement={placement} overlayClassName={styles.popoverContainer}>
        <div slot="content">
          <RiskHitReason
            {...{
              props: {
                ...rest,
              },
            }}
            showTitle={showTitle}
            hitDetails={hitDetails}
            dimensionStrategies={dimensionStrategies}
          />
        </div>
        <QIcon class={styles.hint} type="icon-a-shuomingxian" />
      </Popover>
    );
  },
});

export default RiskHitReasonWrapper;
