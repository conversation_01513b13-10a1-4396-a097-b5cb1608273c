.container{
  .title{
    line-height: 22px;
    padding: 9px 10px;
    display: flex;
    align-items: center;
    gap: 5px;
    border: 1px solid #E4EEF6;
    border-bottom: 0;
    background: #F2F9FC;

    .titleText{
      font-weight: bold;
    }
  }
}

.container + .container{
  margin-top: 15px;
}

.container{
  position: relative;

  :global {
    .ant-spin-dot{
      position: absolute;
      left: 400px;
      top: 50%;
      transform: translate(-50%, -50%);
    }

    .ant-table-row {
      vertical-align: middle !important;
    }
  }
}