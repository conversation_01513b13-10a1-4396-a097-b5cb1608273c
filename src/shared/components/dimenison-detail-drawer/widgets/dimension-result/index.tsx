import { PropType, computed, defineComponent, ref, watch, onMounted } from 'vue';
import { But<PERSON>, Spin } from 'ant-design-vue';
import { cloneDeep } from 'lodash';

import QRichTable from '@/components/global/q-rich-table';
import { monitor } from '@/shared/services';
import { getContent } from '@/utils/content-helper';
import { dynamicDetailJudge, useDynamicRiskDimensionDetail } from '@/hooks/risk-dimension/use-dynamic-risk-dimension';
import { useSearchCompanies } from '@/apps/risk-monitor/pages/targets/hooks/use-search-companies';
import ClampContent from '@/components/clamp-content';
import CompanyStatus from '@/components/global/q-company-status';
import RiskHitReasonWrapper from '@/shared/components/risk-hit-reason-wrapper';

import { TCCONFIG } from '../../config';
import styles from './dimenison-result.module.less';

const DimensionResult = defineComponent({
  name: 'DimensionResult',
  props: {
    strategy: {
      type: Object as PropType<any>,
      default: () => ({}),
    },
    dimensionStrategies: {
      type: Array,
      default: () => [],
    },
    hitDetail: {
      type: Object as PropType<any>,
      default: () => ({}),
    },
    hitTime: {
      type: String,
      default: undefined,
    },
    basicParams: {
      type: Object,
      default: () => ({
        companyId: '03a350311543bd8fbd6d23a4efeafcac',
        dimensionKey: 'RelatedCompanies',
        strategyId: 101212,
        diligenceId: 50177543,
        batchId: 50002631,
        preBatchId: 50002616,
      }),
    },
    // 关联方减少
    hasExtra: {
      type: Boolean,
      default: false,
    },
    extra: {
      type: Object as PropType<any>,
      default: () => ({}),
    },
  },
  setup(props) {
    // 为了使用useSearchCompanies
    const filterValues = ref({
      filters: {
        ...props.basicParams,
        strategyId: props.strategy.strategyId,
        dimensionKey: props.strategy.dimensionKey,
      },
    });

    const searchCompanies = useSearchCompanies(filterValues, monitor.getDynamicContentList);

    const [openDimensionDetail] = useDynamicRiskDimensionDetail();
    // 维度类型，根据维度类型展示不同的表格
    const dimensionKey = computed(() => {
      return props.strategy.dimensionKey;
    });

    const getColoumns = computed(() => {
      return cloneDeep(TCCONFIG[dimensionKey.value] || TCCONFIG.defaultColumns);
    });

    const init = ref(false);

    watch(
      () => searchCompanies.isLoading.value,
      () => {
        init.value = true;
      }
    );

    onMounted(() => {
      searchCompanies.search();
    });

    return {
      init,
      searchCompanies,
      getColoumns,
      dimensionKey,
      openDimensionDetail,
    };
  },
  render() {
    const { searchCompanies, extra } = this;
    let pagigation: any = {
      ...this.searchCompanies.pagination.value,
      pageSizeOptions: ['10', '20', '50'],
      onShowSizeChange: (current, size) => {
        this.searchCompanies.search({ pageIndex: current, pageSize: size });
      },
      onChange: (current) => {
        this.searchCompanies.search({ pageIndex: current });
      },
    };
    let title = this.strategy.strategyName;
    let count = this.searchCompanies.pagination.value.total;
    let dataSource = searchCompanies.data.value?.data ?? [];
    if (this.hasExtra) {
      title = '已失效监控关联方企业';
      dataSource = extra?.outData?.dimensionContent.map((item) => {
        return {
          ...item,
          dimensionContent: {
            ...item,
          },
        };
      });
      count = dataSource?.length;
      pagigation = null;
    }

    // 无数据
    if (this.hasExtra && !dataSource.length && !searchCompanies.isLoading.value) {
      return null;
    }

    // loaidng
    if (!this.init) {
      return <Spin class={styles.container} spinning={true} />;
    }
    return (
      <div class={styles.container}>
        <div class={styles.title}>
          <span class={styles.titleText}>
            【<span domPropsInnerHTML={title}></span>】{count}
            条记录
          </span>
          <RiskHitReasonWrapper placement="right" hitDetails={this.hitDetail} dimensionStrategies={this.dimensionStrategies as any} />
        </div>
        <QRichTable
          rowKey={'dimensionId'}
          showIndex={true}
          columns={this.getColoumns}
          dataSource={dataSource}
          loading={searchCompanies.isLoading.value}
          pagination={pagigation}
          scopedSlots={{
            companyNameRelated: (record) => {
              return (
                <a href={`/embed/companyDetail?keyNo=${record.companyKeynoRelated}&title=${record.companyNameRelated}`} target="_blank">
                  {record.companyNameRelated}
                </a>
              );
            },
            relatedTypeDesc: (record) => {
              return record?.relatedTypeDescList?.join('，') ?? '-';
            },
            riskTypeDescList: (record) => {
              const list = record?.riskTypeDescList ?? [record.shortStatus];
              return (
                <div class="flex" style={{ gap: '4px' }}>
                  {list.map((tag) => {
                    if (!tag) {
                      return '-';
                    }
                    return <CompanyStatus style={{ margin: 0 }} status={tag}></CompanyStatus>;
                  })}
                </div>
              );
            },
            MonitorContent: (item) => {
              return (
                <ClampContent class={'trends-content'} clampKey={item.recordId} line={Infinity}>
                  <span domPropsInnerHTML={getContent(item, this.dimensionKey)}></span>
                </ClampContent>
              );
            },
            hitTime: () => {
              return this.hitTime;
            },
            Action: (record) => {
              if (!dynamicDetailJudge({ ...record, dimensionKey: this.dimensionKey })) {
                return '-';
              }
              return (
                <Button
                  type="link"
                  onClick={() => {
                    this.openDimensionDetail({
                      ...record,
                      dimensionKey: this.dimensionKey,
                      companyId: this.basicParams.companyId,
                      companyName: this.basicParams.companyName,
                    });
                  }}
                >
                  详情
                </Button>
              );
            },
          }}
        ></QRichTable>
      </div>
    );
  },
});

export default DimensionResult;
