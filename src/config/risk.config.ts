import IconRiskLevelGreen from '@/assets/images/icon-risk-level-green.svg';
import IconRiskLevelYellow from '@/assets/images/icon-risk-level-yellow.svg';
import IconRiskLevelRed from '@/assets/images/icon-risk-level-red.svg';

export const RISK_LEVEL = [
  { value: 0, label: '低风险', type: 'success' },
  { value: 1, label: '中风险', type: 'warning' },
  { value: 2, label: '高风险', type: 'danger' },
];

export const RISK_LEVEL_MAP = RISK_LEVEL.reduce(
  (map, { value, label }) => ({
    ...map,
    [value]: label,
  }),
  {}
);

export const getRiskLevelStyle = (value, isMonitor = false) => {
  const result = isMonitor
    ? {
        0: {
          color: ' #333',
          background: '#C4F5E0',
        },
        1: {
          color: ' #333',
          background: '#fec',
        },
        2: {
          color: ' #333',
          background: '#fcc',
        },
      }
    : {
        '-2': {
          color: '#666',
          background: '#E0F5EC',
        },
        '-1': {
          color: '#666',
          background: '#CAE6FC',
        },
        0: {
          color: ' #666',
          background: '#FFEECC',
        },
        1: {
          color: ' #666',
          background: '#FFDECC',
        },
        2: {
          color: ' #666',
          background: '#FFCCCC',
        },
      };
  const style = result[value] || {};
  style.display = 'inline-block';
  style.padding = '1px 4px';
  style.height = '20px';
  style['line-height'] = '18px';
  style.borderRadius = '2px';

  return style;
};

export const getTagStyle = ({ background, color, cursor }) => {
  const style: Record<string, string> = {};
  style.display = 'inline-block';
  style.padding = '1px 4px';
  style.height = '20px';
  style['line-height'] = '18px';
  style.borderRadius = '2px';
  style.background = background;
  style.color = color;
  style.cursor = cursor;
  style.fontSize = '12px';

  return style;
};

export const RiskLevelTextColor = {
  0: '#ffc043', // 低风险
  1: '#ff722d', // 中风险
  2: '#a80000', // 高风险
  '-1': '#128bed', // 提示
  '-2': '#00ad65', // 良好
};

export const RiskLevelTextColorNameMap = {
  0: 'low', // 低风险
  1: 'middle', // 中风险
  2: 'high', // 高风险
  '-1': 'alert', // 提示
  '-2': 'pass', // 良好
};

/**
 *
 * @param value
 * @returns 获取风险等级文字对应的颜色
 */
export const getRiskLevelTextColor = (value) => {
  return RiskLevelTextColor[value] || '#666';
};

export const getRiskLevelTextColorName = (value) => {
  return RiskLevelTextColorNameMap[value];
};

export const RiskResultLevelLabelMapping = {
  0: { value: 0, icon: IconRiskLevelGreen, label: '绿色等级' }, // 绿色
  1: { value: 1, icon: IconRiskLevelYellow, label: '黄色等级' }, // 黄色
  2: { value: 2, icon: IconRiskLevelRed, label: '红色等级' }, // 红色
};

export const BENEFICIARY_RESULT = {
  0: '可正常识别',
  1: '无法正常识别',
};
export const BENEFICIARY_RESULT_COLOR = {
  0: '#00AD65',
  1: '#F04040',
};

export enum MetricTypeEnums {
  /** 尽调指标 */
  Simple = 0,
  /** 监控指标 */
  MonitorMetric = 1,
  /** 监控监管类指标 */
  MonitorSupervisionMetric = 3,
  /** 监控业务类指标 */
  MonitorBusinessMetric = 4,
}

export const MetricsTypeMap = {
  [MetricTypeEnums.MonitorSupervisionMetric]: {
    label: '监管类',
    value: 1,
    style: { color: '#00A3CC', background: '#E0F4F8' },
  },
  [MetricTypeEnums.MonitorBusinessMetric]: {
    label: '业务类',
    value: 2,
    style: { color: '#845FFF', background: '#F0EBFF' },
  },
};
export const MetricsTypes = [
  { label: '监管类', value: MetricTypeEnums.MonitorSupervisionMetric },
  { label: '业务类', value: MetricTypeEnums.MonitorBusinessMetric },
];

export const TrendsStatus = [
  { label: '待处理', value: 0 },
  { label: '跟进中', value: 2 },
  { label: '已跟进', value: 1 },
];

export const FollowUpStatusOptions = [
  { label: '跟进中', value: 2 },
  { label: '已跟进', value: 1 },
];

export const FollowUpLevelOptipons = [
  { label: '非常重要', value: 2 },
  { label: '重要', value: 1 },
  { label: '一般', value: 0 },
];

export const FollowUpCheckOptipons = [
  { label: '无需核实', value: 1 },
  { label: '电话/短信核实', value: 2 },
  { label: '实地核实', value: 3 },
  { label: '网络核实', value: 4 },
  { label: '其他方式', value: 5 },
];

export enum MessageTypeEnum {
  SMS = 1,
  EMAIL = 2,
}
