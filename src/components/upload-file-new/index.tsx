import { PropType, computed, defineComponent, ref, unref } from 'vue';
import { Button, Upload, message } from 'ant-design-vue';
import { isString, uniqBy } from 'lodash';

import QIcon from '@/components/global/q-icon';
import FileIcon from '@/components/file-icon';

import styles from './upload-file-new.module.less';

export type UploadFileStatus = 'error' | 'success' | 'done' | 'uploading' | 'removed';
export interface UploadFile<T = any> {
  uid: string;
  size: number;
  name: string;
  fileName?: string;
  lastModified?: number;
  lastModifiedDate?: Date;
  url?: string;
  status?: UploadFileStatus;
  percent?: number;
  thumbUrl?: string;
  originFileObj?: File | Blob;
  response?: T;
  error?: any;
  linkProps?: any;
  type: string;
  xhr?: T;
  preview?: string;
}

export const UploadFileNew = defineComponent({
  name: 'UploadFileNew',
  props: {
    value: {
      type: Array as PropType<Record<string, any>[]>,
      default: () => [],
    },
  },
  model: {
    event: 'change',
    prop: 'value',
  },
  setup(props, { emit }) {
    const files = computed(() => props.value);
    const cacheFiles = ref<UploadFile[]>([]);
    const handleChange = (params: { file: UploadFile; fileList: UploadFile[] }) => {
      const { file } = params;

      if (file.response?.error) {
        message.error(file.response?.error);
      }
      if (file.status !== 'done') {
        return;
      }
      cacheFiles.value.push(file);
      const data = unref(cacheFiles)
        ?.filter((f) => isString(f.response) && !f.error)
        .map((f) => ({ fileName: f.name, fileUrl: f.response }));
      emit('change', uniqBy([...files.value, ...data], 'fileUrl'));
    };

    const handleRemove = (file) => {
      cacheFiles.value = cacheFiles.value.filter((f) => f.response !== file.fileUrl);
      emit(
        'change',
        files.value.filter((f) => f.fileUrl !== file.fileUrl)
      );
    };

    const handleBeforeUpload = (file: UploadFile, fileList) => {
      if (file.size / 1024 / 1024 > 20) {
        message.open({ type: 'error', content: '文件大小不能超过20M', key: 'error' });
        return false;
      }
      if (unref(files).length + fileList?.length > 5) {
        message.open({ type: 'error', content: '最多只能上传5个附件', key: 'error' });
        return false;
      }
      return true;
    };
    return {
      files,
      cacheFiles,
      handleChange,
      handleRemove,
      handleBeforeUpload,
    };
  },
  render() {
    return (
      <div class={styles.container}>
        <Upload
          multiple
          withCredentials
          action={(file) => `/insights/file/upload?fileName=${file.name}`}
          onChange={this.handleChange}
          showUploadList={false}
          beforeUpload={this.handleBeforeUpload}
          accept=".xls,.xlsx,.csv,.zip,.rar,.7z,.doc,.docx,.pdf,.png,.jpg,.jpeg"
        >
          <div class="flex items-center" style={{ gap: '10px' }}>
            <Button class={styles.button} disabled={this.files?.length >= 5}>
              <QIcon class={styles.iconPlus} type="icon-a-tianjia1x"></QIcon>
              上传
            </Button>
            <span class="text-#999">附件大小不超过20M</span>
          </div>
        </Upload>
        {this.files?.length > 0 && (
          <div style={{ margin: '10px 0 5px' }}>
            {this.files.map((file, index) => {
              const suffix = file?.fileName?.split('.').pop();
              return (
                <div key={index} class={['flex items-center', styles.file]} style={{ margin: '5px 0' }}>
                  <FileIcon type={suffix} />
                  <span style={{ marginLeft: '5px' }}>{file?.fileName || '-'}</span>
                  <a style={{ marginLeft: '10px' }} onClick={() => this.handleRemove(file)}>
                    <QIcon type="icon-icon_yichu" />
                  </a>
                </div>
              );
            })}
          </div>
        )}
      </div>
    );
  },
});
