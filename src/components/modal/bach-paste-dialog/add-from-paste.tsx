import { Button, Checkbox, Input, Popover, Radio, Spin, Tooltip, message } from 'ant-design-vue';
import { computed, defineComponent, ref, ComponentInstance, watch, onMounted, nextTick, set, PropType } from 'vue';
import { sumBy, first, findIndex, cloneDeep, isEmpty } from 'lodash';
import moment from 'moment';

import CompanySelect from '@/components/modal/supplier/company-select';
import { useFetchState } from '@/hooks/use-fetch-state';
import Empty from '@/shared/components/empty';
import Icon from '@/shared/components/icon';
import { company as companyService } from '@/shared/services';
import CompanyLogo from '@/components/company-logo';
import CompanyStatus from '@/components/global/q-company-status';
import { statusCodeMap } from '@/components/global/q-company-status/config';
import QIcon from '@/components/global/q-icon';

import styles from '@/shared/components/text-match-modal/text-match-modal.module.less';

const FailedType = ['linked'];
const FailedStatus = [2, 3, 4];

const DuplicatesCompanyPopover = defineComponent({
  props: {
    options: {
      type: Array as PropType<Array<any>>,
      required: true,
    },
    value: {
      type: String,
    },
  },
  setup() {
    const visible = ref(false);
    return { visible };
  },
  render() {
    return (
      <Popover trigger="click" placement="bottom" v-model={this.visible}>
        <div slot="content" class="space-y-[0.5rem]">
          <div>请选择您需要的企业，系统默认选第一个</div>

          <table class={styles.table}>
            <tbody>
              {this.options.map((item) => {
                return (
                  <tr
                    onClick={async () => {
                      this.visible = false;
                      await nextTick();
                      this.$emit('select', item);
                    }}
                  >
                    <td>
                      <Radio style={{ marginRight: '0' }} checked={this.value === item.KeyNo} value={this.value} />
                    </td>
                    <td align="left" class="space-y-[0.5rem]">
                      <div class="flex items-center" style={{ gap: '8px' }}>
                        <CompanyLogo honorable={false} size={'18px'} id={item.KeyNo} name={item.Name} />
                        <span style={{ display: 'inline-block', width: '240px' }}>{item.Name}</span>
                        <CompanyStatus status={statusCodeMap[item.StatusCode]} />
                      </div>
                      <div class="space-x-[0.5rem] text-14px" style={{ color: 'gray' }}>
                        <span>所属地：{item.Area?.Province}</span>
                        <span style={{ color: '#eee' }}>|</span>
                        <span>成立日期：{moment(item.StartDate * 1000).format('YYYY-MM-DD')}</span>
                      </div>
                    </td>
                  </tr>
                );
              })}
            </tbody>
          </table>
        </div>
        <a
          style={{ marginLeft: '10px' }}
          onClick={() => {
            this.visible = !this.visible;
          }}
        >
          重名
          <QIcon type="icon-a-shixinxia1x1" />
        </a>
      </Popover>
    );
  },
});

const AddFromPaste = defineComponent({
  name: 'AddFromPaste',
  props: {
    visible: {
      type: Boolean,
      required: false,
    },
    placeholder: {
      type: String,
      default: '您可以输入企业名、统一社会信用代码等关键词，系统会自动为您匹配对应企业，不同企业之间请换行。\n\n输入样例：\n企查查\n客找找',
    },
    padding: {
      type: String,
      default: '15px',
    },
  },
  setup(props, { emit }) {
    // 匹配结果
    const itemTextRef = ref<Element>();
    // 待匹配文本
    const matchText = ref('');
    // 是否匹配完成,默认为2，如果去除全部失败匹配，则为1，接口请求完成则为3
    const batchStatus = ref(1);

    const normalErrList = ref<any[]>([]);
    const slurErrList = ref<any[]>([]);
    const rupRightList = ref<any[]>([]);
    const rightList = ref<any[]>([]);
    const rightCount = ref(0);
    const batchResErrList = ref<any[]>([]);
    // 是否提出失败的匹配结果
    const isOutErrorList = ref(false);
    const setMatchResult = (value) => {
      const { normalErr, slurErr, rupRight, right } = value;
      normalErrList.value = normalErr;
      slurErrList.value = slurErr;
      rupRightList.value = rupRight;
      rightList.value = right;
    };
    const handleClear = () => {
      matchText.value = '';
      batchStatus.value = 1;
    };

    const sortedType = ref<'import' | 'matched'>('matched');

    const getNameStringWithSort = (item) => {
      if (item.initMatchType === '精准匹配') {
        // 获取全部需要展示文本
        const itemLink = item.LinkCompany;
        const oriObj = (first(itemLink) as any) || {};
        const oriName = itemLink?.length && oriObj?.OriName ? `<span style="color: #999;">（曾用名：${oriObj?.OriName}）</span>` : '';
        const actName = itemLink?.length && oriObj.ActualName ? oriObj?.ActualName : '';
        return `${item.defaultSort + 1}.${actName}${oriName}`;
      }
      return `${item.defaultSort + 1}.${item.Word}`;
    };

    // 模糊匹配
    const slurCompanies = () => {
      const slurList: any[] = [];
      slurErrList.value.forEach((item) => {
        if (item.Word) {
          slurList.push(item.Word);
        }
      });
      slurErrList.value.forEach((sItem) => {
        if (sItem.LinkedCompany?.Linked || sItem.LinkCompany?.length) {
          sItem.Word = sItem.LinkedCompany?.LinkCompany || sItem.LinkCompany[0].ActualName;
          if (sItem.LinkedCompany?.Linked) {
            sItem.LinkCompany.push({
              Name: sItem.LinkedCompany.LinkCompany,
              ActualName: sItem.LinkedCompany.LinkCompany,
              KeyNo: sItem.LinkedCompany.LinkKeyNo,
            });
          }
        } else {
          // 模糊匹配失败
          sItem.StatusCode = 4;
        }
      });
    };

    /**
     * 批量移除失败匹配
     */
    const removeErrorItem = (arr) => {
      const allRightArr: any[] = [];
      let tempSort = 0;
      arr.forEach((item, index) => {
        if (['精准匹配', '模糊匹配'].includes(item.matchType)) {
          item.defaultSort = tempSort;
          item._str = getNameStringWithSort(item);
          allRightArr.push(item);
          tempSort++;
        }
      });
      return allRightArr;
    };

    // 排序后的匹配结果列表
    const sortedMatchResult = computed(() => {
      const slurAndrRightList = [...slurErrList.value, ...rightList.value];
      if (sortedType.value === 'matched') {
        // 按匹配顺序排序
        slurAndrRightList.sort((a, b) => {
          if (a.sortGroup === b.sortGroup) {
            // 相同分类，优先sortIndex排序
            return a.sortIndex - b.sortIndex;
          }
          return a.sortGroup - b.sortGroup;
        });
      }
      const cList = [...normalErrList.value, ...slurAndrRightList];
      batchResErrList.value = [];
      rightCount.value = 0;
      let defaultSort = 0;
      if (sortedType.value === 'import') {
        // 按导入顺序排序
        cList.sort((a, b) => a.sortIndex - b.sortIndex);
      }

      cList.forEach((cItem) => {
        cItem.defaultSort = defaultSort;
        defaultSort++;

        if (cItem.StatusCode === 1 && !cItem.IsLinked) {
          // LinkCompany数组里增加所属地字段
          cItem.LinkCompany?.forEach((link) => {
            if (link.Area) {
              const { Province, City } = link.Area;
              link.Area.addr = (Province === City ? Province : Province + City) || '-';
            }
          });
        }

        cItem._str = getNameStringWithSort(cItem);

        // 数据打标签，matchType:失败匹配、模糊匹配、精准匹配
        if ([2, 3, 4].includes(cItem.StatusCode) && !cItem.LinkCompany.length) {
          cItem.matchType = '失败匹配';
          batchResErrList.value.push(cItem);
        } else if ((cItem.StatusCode === 1 && !cItem.IsLinked && !['product', 'stock'].includes(cItem.Type)) || cItem.StatusCode === 31) {
          rightCount.value += 1;
          cItem.matchType = '精准匹配';
        } else {
          cItem.matchType = '模糊匹配';
        }
      });
      if (isOutErrorList.value) {
        return removeErrorItem(cList);
      }
      return cList;
    });
    const batchResSlurList = computed(() => {
      return sortedMatchResult.value.filter((item) => item.matchType === '模糊匹配' && item.StatusCode !== 31);
    });
    // 模糊匹配结果
    const fetchData = (params) => {
      return companyService
        .matchCompany({ text: params })
        .then((res) => {
          // 只写入有效数据
          setMatchResult(res);
          slurCompanies();
          batchStatus.value = 3;
        })
        .catch(() => {
          message.error('匹配失败，请重新输入关键词匹配');
        });
    };
    const { execute, isLoading } = useFetchState(fetchData);

    const matchTextArray = computed(() =>
      matchText.value
        .split('\n')
        .filter((t) => !!t)
        .map((t) => t.trim())
    );

    /**
     * 统一输入内容，将支持的符号统一替换为换行符（接口只支持\n）
     */
    const matchTextNormalize = (text: string) => {
      return text.replace(/[,，]/g, '\n');
    };

    /**
     * 获取匹配结果
     */
    const handleMatchText = async () => {
      if (!matchText.value.length) {
        message.error('请输入或粘贴企业名称');
        return;
      }

      matchText.value = matchTextNormalize(matchText.value);

      isOutErrorList.value = false;
      await execute(matchText.value);
    };

    const textarea = ref<ComponentInstance | null>(null);

    const hightlight = ref();
    onMounted(async () => {
      await nextTick();
      (textarea.value?.$el as HTMLTextAreaElement)?.focus?.();
    });

    const handleRemoveFromResult = (index) => {
      function findingIndex(dataList, targetIndex) {
        return findIndex(dataList, (rl: any) => rl.sortIndex === targetIndex);
      }
      const delItem = sortedMatchResult.value[index];
      let delIndex: number | null = null;
      if (delItem.matchType === '精准匹配' && delItem.StatusCode !== 31 && !['hk', 'tw'].includes(delItem.Type)) {
        delIndex = findingIndex(rightList.value, delItem.sortIndex);
        if (delIndex !== -1) {
          rightList.value.splice(delIndex, 1);
        } else {
          delIndex = findingIndex(rupRightList.value, delItem.sortIndex);
          rupRightList.value.splice(delIndex, 1);
        }
      } else {
        delIndex = findingIndex(normalErrList.value, delItem.sortIndex);
        if (delIndex !== -1) {
          normalErrList.value.splice(delIndex, 1);
        } else {
          delIndex = findingIndex(slurErrList.value, delItem.sortIndex);
          slurErrList.value.splice(delIndex, 1);
        }
      }
      if (!sortedMatchResult.value.length) {
        batchStatus.value = 1;
      }
    };

    const editCompany = (searchItem, cIndex) => {
      if (isEmpty(searchItem)) {
        return;
      }
      const editCompanyItem = cloneDeep(sortedMatchResult.value[cIndex]);
      const editName = searchItem.value;
      editCompanyItem.Word = editName;
      editCompanyItem.isEdit = false;
      editCompanyItem.StatusCode = 1;
      editCompanyItem.LinkCompany.unshift({
        Name: editName,
        ActualName: editName,
        KeyNo: searchItem.id,
        companyId: searchItem.id,
      });

      const companyItem = sortedMatchResult.value[cIndex];
      // 判断属于哪个数组
      let errArrIndex: number | null = null;
      errArrIndex = findIndex(normalErrList.value, (rl) => rl.sortIndex === companyItem.sortIndex);
      if (errArrIndex !== -1) {
        normalErrList.value.splice(errArrIndex, 1, editCompanyItem);
      } else {
        errArrIndex = findIndex(slurErrList.value, (rl) => rl.sortIndex === companyItem.sortIndex);
        editCompanyItem.StatusCode = 31;

        slurErrList.value.splice(errArrIndex, 1, editCompanyItem);
      }
    };
    /**
     * 重名企业选择
     */
    const handleRadio = (companyItem, linkItem) => {
      const slurIndex = findIndex(slurErrList.value, (rl) => rl.sortIndex === companyItem.sortIndex);
      if (slurIndex !== -1) {
        // 模糊的重名
        slurErrList.value[slurIndex].LinkCompany = [linkItem];
        slurErrList.value[slurIndex].hasEntSameName = false;
        slurErrList.value[slurIndex].Word = linkItem.ActualName;
      } else {
        // 精准的重名
        const rightIndex = findIndex(rightList.value, (rl) => rl.sortIndex === companyItem.sortIndex);
        rightList.value[rightIndex].LinkCompany = [linkItem];
        rightList.value[rightIndex].hasEntSameName = false;
        rightList.value[rightIndex].Word = linkItem.ActualName;
      }
    };
    watch(
      () => sortedMatchResult.value,
      (val) => {
        emit(
          'change',
          val.reduce((arr, cur) => {
            if (cur.LinkCompany?.length) {
              arr = [...arr, cur.LinkCompany[0]];
            } else if (cur.LinkedCompany?.length) {
              arr = [...arr, cur.LinkedCompany[0]];
            }
            return arr;
          }, [])
        );
      },
      {
        immediate: true,
        deep: true,
      }
    );

    const handleTrack = (btnName: string) => {
      emit('track', btnName);
    };

    return {
      batchStatus,
      matchText,
      sortedMatchResult,
      handleClear,
      isLoading,
      handleMatchText,
      handleRemoveFromResult,
      textarea, // ref
      hightlight,
      matchTextArray,
      sortedType,
      itemTextRef,
      handleTrack,
      rightCount,
      normalErrList,
      slurErrList,
      rupRightList,
      rightList,
      batchResErrList,
      batchResSlurList,
      isOutErrorList,
      editCompany,
      handleRadio,
    };
  },
  render() {
    return (
      <div class={styles.container} style={{ padding: this.padding }}>
        <div class={[styles.column]}>
          <div class={styles.result}>
            <header class={styles.header}>
              <div class={styles.title}>{this.batchStatus === 3 ? '文本识别' : '编辑或粘贴文本'}</div>
              <div class={styles.extra} v-show={this.matchText?.length}>
                <div
                  class={styles.clear}
                  onClick={() => {
                    this.handleTrack('清空');
                    this.handleClear();
                  }}
                >
                  <Icon class={styles.icon} type="icon-a-shanchuxian1x" />
                  <span>清空</span>
                </div>
              </div>
            </header>

            <div style={{ overflow: 'hidden', height: 'inherit' }}>
              <Input.TextArea
                ref="textarea"
                v-model={this.matchText}
                class={styles.text}
                size="large"
                placeholder={this.placeholder}
                disabled={this.isLoading}
                v-show={this.batchStatus !== 3}
                maxLength={3000}
              />
              <div
                class={styles.text}
                style={{ cursor: 'pointer', overflow: 'auto' }}
                v-show={this.batchStatus === 3}
                onClick={() => {
                  this.batchStatus = 2;
                }}
              >
                {this.matchTextArray.map((text, index, arr) => {
                  const matched = this.sortedMatchResult.find((item) => item.Word === text && !FailedStatus.includes(item.StatusCode));
                  const StartOffSet = sumBy(arr.slice(0, index), (str) => str.length) + index;
                  const isHightLight = StartOffSet === this.hightlight?.Location?.StartOffSet;
                  return [
                    <span
                      class={styles[matched?.Type]}
                      style={{
                        backgroundColor: isHightLight ? '#eee' : '',
                        fontWeight: matched?.Type ? 'bold' : '',
                      }}
                      key={index}
                    >
                      {text}
                    </span>,
                    <br />,
                  ];
                })}
              </div>
              <div class={styles.count}>
                <span>{this.matchText?.length}</span>/<span>{3000}</span>
              </div>
            </div>
          </div>
        </div>
        <div class={[styles.column, styles.action]}>
          <Button
            class={styles.button}
            disabled={this.batchStatus === 3}
            type="primary"
            onClick={() => {
              this.handleMatchText();
              this.handleTrack('匹配');
            }}
          >
            <span class={styles.buttonText}>匹配</span>
            <Icon class={styles.icon} type="icon-a-xianduanyou" />
          </Button>
        </div>
        <div class={styles.column}>
          <div class={styles.result}>
            <header class={styles.header}>
              <div class={styles.title} v-show={this.batchStatus !== 3}>
                匹配结果
              </div>
              <div class="flex" v-show={this.batchStatus === 3}>
                {[
                  {
                    type: 'company',
                    text: '精准匹配',
                    color: '#333',
                    count: this.rightCount,
                  },
                  {
                    type: 'untag',
                    text: '匹配失败',
                    color: '#F04040',
                    count: this.batchResErrList.length,
                  },
                  {
                    type: 'linked',
                    text: '模糊匹配',
                    color: '#ff8900',
                    count: this.batchResSlurList.length,
                  },
                ]
                  .filter((item) => item.count)
                  .map((item) => (
                    <div class={[styles.legend, styles[item.type]]}>
                      <span style={{ color: '#333' }}>{item.text}</span>
                      <span class={styles.matchCount}>{item.count}</span>
                    </div>
                  ))}
              </div>
              {this.$slots.rightTitle}
            </header>
            {this.isLoading ? (
              <Spin class={styles.loading} spinning={this.isLoading} />
            ) : (
              [
                <div class={styles.empty} v-show={this.batchStatus === 1 && this.sortedMatchResult.length === 0}>
                  <Empty type="search" description="暂无数据" />
                </div>,
                <div class={styles.content}>
                  <div v-show={this.batchResErrList.length} class={[styles.clistItemDanger, styles.clistItemAction]}>
                    系统匹配失败以下<b>{this.batchResErrList.length}</b>家，您可以
                    <Checkbox v-model={this.isOutErrorList}>全部删除</Checkbox>
                  </div>
                  {this.sortedMatchResult.map((item, index) => {
                    const { isEdit } = item;
                    const isUnMatched = [3].includes(item.StatusCode) && this.sortedType === 'matched';
                    const editNode = (
                      <div
                        class="flex items-center justify-between"
                        style={{
                          padding: '0 5px 0 5px',
                          gap: '15px',
                          background: isUnMatched ? '#fff7f7' : '',
                        }}
                      >
                        <CompanySelect
                          defaultOpen
                          autoFocus
                          immediate
                          value={String(item.FWord).replace('\n', '')}
                          style={{ flex: '1' }}
                          allowClear
                          onChange={(text, val) => this.editCompany(val, index)}
                        />
                        <a
                          style={{ whiteSpace: 'nowrap', color: '#808080' }}
                          onClick={() => {
                            set(item, 'isEdit', false);
                          }}
                        >
                          取消
                        </a>
                      </div>
                    );
                    const textOverflowLength = 20;
                    const oldName = item.LinkCompany?.[0]?.OriName;
                    const name = `${index + 1}.${item.Name || item.Word}${oldName ? `（曾用名：${item.FWord}）` : ''}`;
                    const labelName = (
                      <span>
                        <span domPropsInnerHTML={item._str} />
                        {oldName && <span domPropsInnerHTML={item.str}></span>}
                      </span>
                    );
                    const isTextOverflow = name?.length > textOverflowLength;
                    const ColorMap = {
                      失败匹配: '#F04040',
                      模糊匹配: '#6171FF',
                    };
                    const node = (
                      <div
                        class={[styles.item]}
                        data-testid="match-content"
                        style={{
                          background: isUnMatched ? '#fff7f7' : '',
                        }}
                        key={index}
                      >
                        <Tooltip title={labelName} mouseEnterDelay={isTextOverflow ? 0 : 99999}>
                          <span
                            data-id={`match-item-${index}`}
                            data-type={item.Type}
                            class={[styles.itemText, styles[item.Type]]}
                            style={{ color: ColorMap[item.matchType] || '#333' }}
                            {...{
                              on: {
                                mouseenter: () => {
                                  this.hightlight = item;
                                },
                                mouseleave: () => {
                                  this.hightlight = undefined;
                                },
                              },
                            }}
                          >
                            {labelName}
                          </span>
                        </Tooltip>
                        {item.Duplicates && <span class={styles.tag}>重复</span>}
                        {item.LinkCompany?.length > 1 && (
                          <DuplicatesCompanyPopover
                            value={item.KeyNo || item.LinkCompany[0].KeyNo}
                            options={item.LinkCompany}
                            onSelect={(val) => {
                              this.handleRadio(item, val);
                            }}
                          />
                        )}

                        <div class="flex items-center space-x-[1rem]" style={{ marginLeft: '8px', flex: '1' }}>
                          <Icon
                            data-testid="paste-edit"
                            onClick={() => {
                              set(item, 'isEdit', true);
                            }}
                            type="icon-a-bianjigenjin1x"
                            v-show={FailedStatus.includes(item.StatusCode) || FailedType.includes(item.Type)}
                            class={styles.edit}
                          />
                          <Icon
                            data-testid="paste-delete"
                            onClick={() => {
                              this.handleTrack('删除');
                              this.handleRemoveFromResult(index);
                            }}
                            class={styles.remove}
                            type="icon-chahao"
                          />
                        </div>
                        {item.StatusCode === 2 && (
                          <span class="flex" style={{ alignSelf: 'end', color: '#F04040', marginLeft: '10px' }}>
                            仅支持境内企业
                          </span>
                        )}
                      </div>
                    );
                    return [isEdit ? editNode : node];
                  })}
                </div>,
              ]
            )}
            <div class={[styles.footer, 'flex']} v-show={this.batchStatus === 3}>
              <a
                class={styles.footerButton}
                onClick={() => {
                  this.handleTrack({ import: '按匹配顺序', matched: '按导入顺序' }[this.sortedType]);
                  this.sortedType = this.sortedType === 'import' ? 'matched' : 'import';
                }}
              >
                <q-icon type="icon-sort"></q-icon>
                <span style={{ marginLeft: '4px' }}>{{ import: '按匹配顺序', matched: '按导入顺序' }[this.sortedType]}</span>
              </a>
            </div>
          </div>
        </div>
      </div>
    );
  },
});

export default AddFromPaste;
