@import '@/styles/token.less';

@keyframes rotate-clockwise {
  0% {
    transform: translate(-50%, -50%) rotate(0);
  }

  100% {
    transform: translate(-50%, -50%) rotate(360deg);
  }
}

@keyframes rotate-counterclockwise {
  0% {
    transform: translate(-50%, -50%) rotate(0);
  }

  100% {
    transform: translate(-50%, -50%) rotate(-360deg);
  }
}

@keyframes twinkling {
  0% {
    opacity: 1;
  }

  50% {
    opacity: 0.4;
  }

  100% {
    opacity: 1;
  }
}

@size-sm: 20px;
@size-default: 40px;
@size-lg: 60px;
@animation-during: 1.333s;

.logo-size(@size) {
  width: @size;
  height: @size;

  .p1 {
    width: 32px / @size-default * @size;
    height: 32px / @size-default * @size;
  }

  .p2 {
    width: 20px / @size-default * @size;
    height: 20px / @size-default * @size;
  }

  .p3 {
    width: 8px / @size-default * @size;
    height: 8px / @size-default * @size;
  }
}

.name-size(@size) {
  padding-left: 10px / @size-default * @size;

  img {
    width: 108px / @size-default * @size;
    height: 34px / @size-default * @size;
  }
}

.fullsize {
  width: 100%;
  height: 100%;
  display: flex;
  align-items: center;
  justify-content: center;
}

.root {
  position: relative;
  display: inline-flex;
  align-items: center;
  justify-content: center;
  width: 170px !important;
  height: @size-default !important;
}

.sm,
:global(.ant-spin-sm) .root {
  width: 170px / @size-default * @size-sm;
  height: @size-sm;

  .logo {
    .logo-size(@size-sm);
  }

  .name {
    .name-size(@size-sm);
  }
}

.lg,
:global(.ant-spin-lg) .root {
  width: 170px / @size-default * @size-lg;
  height: @size-lg;

  .logo {
    .logo-size(@size-lg);
  }

  .name {
    .name-size(@size-lg);
  }
}

.logo {
  position: relative;
  border-radius: 18.145%;
  background: #e0e0e0;
  .logo-size(@size-default);

  img {
    position: absolute;
    left: 50%;
    top: 50%;
    transform-origin: center;
    transform: translate(-50%, -50%);
    animation: @animation-during ease infinite;
  }

  .p2 {
    animation-name: rotate-counterclockwise;
  }

  .p3 {
    animation-name: rotate-clockwise;
  }
}

.name {
  display: flex;
  align-items: center;
  animation: twinkling @animation-during linear infinite;
  .name-size(@size-default);
}

:global(.ant-spin-nested-loading) > div .root {
  margin: 0 !important;
  transform: translate(-50%, -50%);
}

:global(.ant-spin) > .root {
  display: flex;
}
