// Vitest Snapshot v1, https://vitest.dev/guide/snapshot.html

exports[`Notification > Renders with nodata 1`] = `
<hero-layout innerstyle="[object Object]">
  <div class="root">
    <div class="header">
      <div class="wrapper border">
        <div class="title">消息中心</div>
        <div class="extra"><span data-testid="readAll" class="abtn"><q-icon-stub type="icon-querenruwei1"></q-icon-stub> 全部已读</span></div>
      </div>
    </div>
    <div class="body">
      <div class="loading">
        <div class="fullsize" style="background-color: unset;"><span class="root" style="background-color: unset;"><div class="logo"><img src="/src/components/global/q-loading/images/part-1.svg" class="p1"><img src="/src/components/global/q-loading/images/part-2.svg" class="p2"><img src="/src/components/global/q-loading/images/part-3.svg" class="p3"></div><div class="name"><img src="/src/components/global/q-loading/images/qcc.svg" alt="企查查"></div></span></div>
      </div>
    </div>
  </div>
</hero-layout>
`;
