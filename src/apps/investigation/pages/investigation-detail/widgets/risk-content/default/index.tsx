import { computed, defineComponent, nextTick, PropType, ref } from 'vue';
import { Button } from 'ant-design-vue';

import { uesInvestStore } from '@/hooks/use-invest-store';
import QCard from '@/components/global/q-card';
import FullWatermark from '@/components/full-watermark';
import DiligenceRiskResultDashboard from '@/components/diligence-risk-result-dashboard';
import QSwitch from '@/components/global/q-switch';
import { useExpandKeys } from '@/apps/investigation/pages/investigation-detail/hook/use-expand-keys';
import QRichTableEmpty from '@/components/global/q-rich-table/components/empty';
import QIcon from '@/components/global/q-icon';

import RiskFilter from '../../risk-filter';
import RiskReview from '../../risk-review';
import RiskResultMultiTab from '../widgets/risk-result-multi-tab';
import RiskGroup from '../../risk-group';

const useRiskGroup = (list) => {
  // 当前选中的分组ID
  const currentGroupId = ref(-1);

  const isDefaultGroup = computed(() => currentGroupId.value === -1);

  const resetCurrentGroup = () => {
    currentGroupId.value = -1;
  };

  // 分组列表去空值
  const groups = computed(() => {
    if (!list.value?.length) {
      return [];
    }
    // 科创模型会有totalHits为0但需要展示的维度
    return list.value.filter((v) => v.totalHits > 0 || v.scoreDetails?.length > 0);
  });

  // 选中的分组
  const currentGroup = computed(() => {
    if (currentGroupId.value === -1) {
      return groups.value;
    }
    const curr = groups.value.find((v) => v.groupDefinition.groupId === currentGroupId.value);
    return curr ? [curr] : [];
  });
  const handleGroupChange = (key) => {
    currentGroupId.value = key;
  };
  return {
    isDefaultGroup,
    currentGroup,
    currentGroupId,
    groups,
    handleGroupChange,
    resetCurrentGroup,
  };
};

const EmptyResult = defineComponent({
  name: 'EmptyResult',
  render() {
    return (
      <div style={{ border: '1px solid #eee' }}>
        <QRichTableEmpty minHeight={'350px'} size={'100px'}>
          <div>当前企业未检测到相关风险项</div>
        </QRichTableEmpty>
      </div>
    );
  },
});

const ErrorResult = defineComponent({
  name: 'ErrorResult',
  props: {
    errorInfo: {
      type: Object,
      default: () => ({}),
    },
  },
  render() {
    return (
      <QRichTableEmpty minHeight={'400px'} size={'100px'} style={{ textAlign: 'center' }}>
        <div style={{ color: '#a80000', fontWeight: '700', display: 'flex', alignItems: 'center', gap: '5px' }}>
          <QIcon type="icon-zhuyi" />
          <span>{this.errorInfo.code === 400203 ? this.errorInfo.message : '发生未知错误,请重新发起准入尽调'}</span>
        </div>
        {this.errorInfo.code === 400203 ? null : (
          <Button style={{ marginTop: '10px' }} type="primary" onClick={() => this.$emit('retry')}>
            重新排查
          </Button>
        )}
      </QRichTableEmpty>
    );
  },
});

const RiskContentDefault = defineComponent({
  name: 'RiskContentDefault',
  props: {
    loading: {
      type: Boolean,
      default: true,
    },
    company: {
      type: Object,
      required: true,
    },
    riskLevel: {
      type: Number,
      required: true,
    },
    /**
     * 距离页面的距离
     */
    offset: {
      type: Object as PropType<{ x: number; y: number }>,
      default: () => ({ x: 0, y: 0 }),
    },
    /**
     * 面包屑的高度
     */
    breadcrumbHeight: {
      type: Number,
      default: 0,
    },
    riskInfo: {
      type: Object,
      default: () => ({}),
    },
    allModelRiskInfoList: {
      type: Array as PropType<Record<string, any>[]>,
      default: () => [],
    },
    riskModels: {
      type: Object as PropType<Record<string, any>>,
      default: () => ({}),
    },
    errorInfo: {
      type: Object as PropType<{ error: boolean; message: string | undefined; code: number }>,
      default: () => ({}),
    },
  },
  setup(props) {
    const { currentSelectModelID, isCurrentDDResultsDesc } = uesInvestStore();
    const currentTabIndex = ref(0);

    // Watermark size
    const watermarkHeight = computed(() => window.innerHeight - props.offset.y);

    const groupMetricScores = computed(() => props.riskInfo?.details?.groupMetricScores);

    const { groups, currentGroupId, isDefaultGroup, resetCurrentGroup, handleGroupChange, currentGroup } = useRiskGroup(groupMetricScores);

    const changeSelectModelID = (id: number) => {
      currentSelectModelID.value = id;
      resetCurrentGroup();
    };

    const { setExpandKeys, hasAllMetricExpanded } = useExpandKeys();
    const expandAllMetricKeys = () => {
      const metricIds = groupMetricScores.value?.flatMap(({ scoreDetails }) => {
        return scoreDetails?.map(({ metricsId }) => metricsId.toString());
      });
      setExpandKeys(metricIds);
    };
    const onToggleExpandAll = () => {
      const checked = !hasAllMetricExpanded.value;
      if (checked) {
        // 所有指标的ID
        expandAllMetricKeys();
      } else {
        setExpandKeys([]);
      }
      hasAllMetricExpanded.value = checked;
    };

    const highlightModelId = ref();
    const handleHighlightModel = (item) => {
      highlightModelId.value = item?.id;
    };

    const handleScroll = async (id) => {
      resetCurrentGroup();
      // 展开对应的风险维度模块
      setExpandKeys([id], false);
      await nextTick();
      const element = document.getElementById(id);
      if (!element) {
        return;
      }

      // 滚动到对应的风险维度模块
      element.scrollIntoView({ block: 'start' });

      // 回弹到正确位置（目前由于 滚动区域 和顶部导航的问题，需要额外的偏移量才能完整显示）
      if (props.breadcrumbHeight !== 0) {
        const offsetTop = props.breadcrumbHeight * -1; // 向上偏移量
        const scrollWrapper = document.getElementById('workbench-layout-main') || window;
        scrollWrapper.scrollBy(0, offsetTop);
      }
    };

    /** 合并模型设置中的（定性）风险等级信息 */
    const riskInfoList = computed(() => {
      return props.allModelRiskInfoList.map((v) => {
        const riskModel = props.riskModels[v.orgModelId];
        const resultSetting = riskModel?.resultSetting ?? [];
        const riskLevelInfo = resultSetting.find((rs) => rs.level === v.result);
        return {
          ...v,
          riskName: riskLevelInfo?.name,
          riskLevel: riskLevelInfo?.level,
        };
      });
    });

    const currentRiskInfo = computed(() => {
      const riskModel = props.riskModels[props.riskInfo.orgModelId];
      const resultSetting = riskModel?.resultSetting ?? [];
      const riskLevelInfo = resultSetting.find((rs) => rs.level === props.riskInfo.result);
      return {
        ...props.riskInfo,
        riskName: riskLevelInfo?.name,
        riskLevel: riskLevelInfo?.level,
      };
    });

    return {
      watermarkHeight,
      currentTabIndex,
      changeSelectModelID,
      currentGroupId,
      handleGroupChange,
      groups,
      isDefaultGroup,
      isCurrentDDResultsDesc,
      currentGroup,
      hasAllMetricExpanded,
      setExpandKeys,
      onToggleExpandAll,
      currentSelectModelID,
      highlightModelId,
      handleHighlightModel,
      handleScroll,

      riskInfoList,
      currentRiskInfo,
    };
  },
  render() {
    const flexStyle = {
      display: 'flex',
      flexDirection: 'column',
      flex: 1,
    };

    const props = this;
    if (props.loading) {
      return <div style="height: 0" />;
    }

    // 尽调错误: 接口报错等
    if (props.errorInfo.error) {
      return (
        <QCard rootStyle={flexStyle}>
          <ErrorResult errorInfo={this.errorInfo} onRetry={() => this.$emit('retry')} />
        </QCard>
      );
    }

    const renderDimensionDetail = () => {
      return (
        <div>
          {/* 排查结果 */}
          <QCard bodyStyle={{ padding: '16px' }}>
            <RiskReview
              scrollOffsetTop={this.breadcrumbHeight}
              riskLevel={this.riskInfo.result}
              riskInfo={this.currentRiskInfo}
              onScroll={this.handleScroll}
            />
          </QCard>
          {/* 风险维度 */}
          {this.groups.length > 0 ? (
            <RiskFilter
              style={{ margin: '8px 0' }}
              defaultActiveKey={this.currentGroupId}
              tabs={this.groups}
              onChange={this.handleGroupChange}
            >
              <div slot="extra" class="flex items-center" style="gap: 8px; cursor: pointer" onClick={() => this.onToggleExpandAll()}>
                <QSwitch size="small" checked={this.hasAllMetricExpanded} />
                <span>一键展开</span>
              </div>
            </RiskFilter>
          ) : null}
          {/* 未检测到风险 */}
          {this.groups.length === 0 ? (
            <QCard bodyStyle={{ padding: '16px' }}>
              <EmptyResult />
            </QCard>
          ) : null}
          {this.currentGroup.map((item) => {
            return (
              <RiskGroup
                key={item.groupDefinition.groupId}
                showTitle={this.isDefaultGroup}
                timeStamp={Date.now()}
                riskInfo={this.riskInfo}
                diligenceData={item}
                diligenceInfo={{
                  companyName: props.company.Name,
                  keyNo: props.company.KeyNo,
                  snapshotId: this.riskInfo.snapshotId,
                }}
              />
            );
          })}
        </div>
      );
    };

    return (
      <FullWatermark
        height={this.watermarkHeight}
        offset={{
          x: props.offset.x,
          y: props.offset.y,
        }}
        fillOffset={{
          x: 300,
          y: 300,
        }}
        style={flexStyle}
      >
        {this.riskInfoList.length > 1 ? (
          <QCard
            rootStyle={{
              position: 'sticky',
              top: `${this.breadcrumbHeight}px`,
              zIndex: 10,
              marginBottom: '8px',
            }}
            bodyStyle={{ padding: '16px' }}
          >
            <RiskResultMultiTab
              list={this.riskInfoList}
              currentSelectModelID={this.currentSelectModelID}
              highlightId={this.highlightModelId}
              onHover={(item) => this.handleHighlightModel(item)}
              onChange={(item) => {
                this.changeSelectModelID(item.id);
                this.hasAllMetricExpanded = false;
                this.setExpandKeys([]);
              }}
            />
          </QCard>
        ) : null}

        {/* 排查尽调综述 */}
        {this.isCurrentDDResultsDesc ? (
          <QCard rootStyle={{ ...flexStyle, marginTop: 0 }} bodyStyle={{ padding: '16px' }}>
            <DiligenceRiskResultDashboard
              list={this.riskInfoList}
              highlightId={this.highlightModelId}
              onModelClick={(item) => this.changeSelectModelID(item.id)}
              onHover={(item) => this.handleHighlightModel(item)}
            />
          </QCard>
        ) : null}

        {/* 排查结果 */}
        {this.isCurrentDDResultsDesc ? null : renderDimensionDetail()}
      </FullWatermark>
    );
  },
});

export default RiskContentDefault;
