// Vitest Snapshot v1, https://vitest.dev/guide/snapshot.html

exports[`RiskContentDefault > 单排查模型 1`] = `
<fullwatermark-stub height="768" offset="[object Object]" filloffset="[object Object]">
  <div>
    <div class="root">
      <div class="body" style="padding: 16px;">
        <div class="container bg3">
          <section class="container">
            <div class="container"><img src="file:///src/components/diligence-risk-level/imgs/1.svg" width="151">
              <div class="text" style="color: #FFAA00;">
                <div class="riskScore"></div>
                <div style="color: #ff722d;">Low Risk</div>
              </div>
            </div>
            <div class="riskBlock">
              <ul class="riskDetail">
                <li class="dot riskItem noHit highRisk">
                  <div class="label"><span class="safe levelIcon"><img src="/src/assets/images/icon-risk-level-red.svg" width="22" height="22" class="icon">红色等级</span><i class="safe riskNum">0项：</i></div>
                  <div class="content safe">未检测到相关项</div>
                </li>
                <li class="dot riskItem noHit">
                  <div class="label"><span class="safe levelIcon"><img src="/src/assets/images/icon-risk-level-yellow.svg" width="22" height="22" class="icon">黄色等级</span><i class="safe riskNum">0项：</i></div>
                  <div class="content safe">未检测到相关项</div>
                </li>
                <li class="dot riskItem noHit">
                  <div class="label"><span class="safe levelIcon"><img src="/src/assets/images/icon-risk-level-green.svg" width="22" height="22" class="icon">绿色等级</span><i class="safe riskNum">0项：</i></div>
                  <div class="content safe">未检测到相关项</div>
                </li>
              </ul>
            </div>
            <div>
              <div class="switchButton">
                <div class="sortBy">
                  <q-icon-stub type="icon-qiehuan"></q-icon-stub><span>按数量</span>
                </div>
              </div>
            </div>
          </section>
        </div>
      </div>
    </div>
    <div class="container" style="margin: 8px 0px;">
      <div class="itemList">
        <div class="item active">全部</div>
        <div class="item"><span class="count">1</span></div>
      </div>
      <div>
        <div class="flex items-center" style="gap: 8px; cursor: pointer;"><span class="qSwitchWrapper small"><button type="button" role="switch" class="ant-switch ant-switch-small"><span class="ant-switch-inner"></span></button></span><span>一键展开</span></div>
      </div>
    </div>
    <div class="container">
      <div class="groupHeader"><span></span><span>1</span></div>
      <div class="groupContent"></div>
    </div>
  </div>
</fullwatermark-stub>
`;

exports[`RiskContentDefault > 多排查模型时 1`] = `
<fullwatermark-stub height="768" offset="[object Object]" filloffset="[object Object]">
  <div class="root" style="position: sticky; top: 0px; z-index: 10; margin-bottom: 8px;">
    <div class="body" style="padding: 16px;">
      <ul class="container">
        <li class="item overview undefined">
          <div class="title"><span class="icon"><img src="/src/apps/investigation/pages/investigation-detail/widgets/risk-content/widgets/risk-result-multi-tab/img/icon-dd-all.svg" width="22"></span><span class="text">尽调综述</span></div>
          <div class="risk" style="display: none;"></div>
        </li>
        <li class="item middle hover">
          <div class="title"><span class="icon"><q-icon-stub type="icon-icon_fengxianmoxing1"></q-icon-stub></span><span class="text"></span></div>
          <div class="risk">Low Risk</div>
        </li>
        <li class="item high hover">
          <div class="title"><span class="icon"><q-icon-stub type="icon-icon_fengxianmoxing1"></q-icon-stub></span><span class="text"></span></div>
          <div class="risk">Medium Risk</div>
        </li>
      </ul>
    </div>
  </div>
  <div>
    <div class="root">
      <div class="body" style="padding: 16px;">
        <div class="container bg3">
          <section class="container">
            <div class="container"><img src="file:///src/components/diligence-risk-level/imgs/1.svg" width="151">
              <div class="text" style="color: #FFAA00;">
                <div class="riskScore"></div>
                <div style="color: #ff722d;">Low Risk</div>
              </div>
            </div>
            <div class="riskBlock">
              <ul class="riskDetail">
                <li class="dot riskItem noHit highRisk">
                  <div class="label"><span class="safe levelIcon"><img src="/src/assets/images/icon-risk-level-red.svg" width="22" height="22" class="icon">红色等级</span><i class="safe riskNum">0项：</i></div>
                  <div class="content safe">未检测到相关项</div>
                </li>
                <li class="dot riskItem noHit">
                  <div class="label"><span class="safe levelIcon"><img src="/src/assets/images/icon-risk-level-yellow.svg" width="22" height="22" class="icon">黄色等级</span><i class="safe riskNum">0项：</i></div>
                  <div class="content safe">未检测到相关项</div>
                </li>
                <li class="dot riskItem noHit">
                  <div class="label"><span class="safe levelIcon"><img src="/src/assets/images/icon-risk-level-green.svg" width="22" height="22" class="icon">绿色等级</span><i class="safe riskNum">0项：</i></div>
                  <div class="content safe">未检测到相关项</div>
                </li>
              </ul>
            </div>
            <div>
              <div class="switchButton">
                <div class="sortBy">
                  <q-icon-stub type="icon-qiehuan"></q-icon-stub><span>按数量</span>
                </div>
              </div>
            </div>
          </section>
        </div>
      </div>
    </div>
    <div class="container" style="margin: 8px 0px;">
      <div class="itemList">
        <div class="item active">全部</div>
        <div class="item"><span class="count">1</span></div>
        <div class="item"><span class="count">2</span></div>
      </div>
      <div>
        <div class="flex items-center" style="gap: 8px; cursor: pointer;"><span class="qSwitchWrapper small"><button type="button" role="switch" class="ant-switch ant-switch-small"><span class="ant-switch-inner"></span></button></span><span>一键展开</span></div>
      </div>
    </div>
    <div class="container">
      <div class="groupHeader"><span></span><span>1</span></div>
      <div class="groupContent"></div>
    </div>
    <div class="container">
      <div class="groupHeader"><span></span><span>2</span></div>
      <div class="groupContent"></div>
    </div>
  </div>
</fullwatermark-stub>
`;
