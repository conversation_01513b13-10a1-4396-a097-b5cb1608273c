.container {
  display: flex;
  gap: 10px;
  border-bottom: 1px solid #128bed;
  overflow-x: auto;

  & {
    scrollbar-width: none; /* Firefox */
    -ms-overflow-style: none; /* IE 10+ */
  }

  &::-webkit-scrollbar {
    display: none; /* Chrome, Safari */
  }

  .item {
    min-width: 170px;
    height: 56px;
    background-color: rgba(#128bed, 0.1);
    flex-direction: column;
    align-items: center;
    justify-content: center;
    color: #333;
    border-top-left-radius: 2px;
    border-top-right-radius: 2px;
    font-size: 16px;
    line-height: 24px;
    cursor: pointer;
    display: flex;
    gap: 2px;
    padding: 0 10px;
    transition: background-color 0.15s linear;

    .title {
      display: flex;
      align-items: center;
      gap: 5px;
      line-height: 24px;
      font-weight: 700;
    }

    .icon {
      font-size: 24px;
      color: #999;

      > img,
      > svg {
        display: block;
      }
    }

    .risk {
      padding: 1px 4px;
      border-radius: 2px;
      line-height: 20px;
      font-size: 12px;
      text-align: center;
      background: #fff;
      border: 0.5px solid #E3E3E3;
    }

    .text {
      max-width: 8em;
      overflow: hidden;
      text-overflow: ellipsis;
      white-space: nowrap;
    }

    &.overview {
      .title {
        gap: 10px;
      }
    }

    &.hover,
    &:hover {
      background: #e2f1fd;
      color: #128bed;
    }

    &.active {
      background: #128bed;
      color: #fff;
      cursor: default;

      .icon {
        color: #fff;
      }
    }

    // 0: 警示 #ff5e71
    &.low {
      .risk {
        color: #ffc043;
      }
    }
    // 1: '#ff722d', // 中风险
    &.middle {
      .risk {
        color: #ff722d;
      }
    }
    // 2: '#a80000', // 高风险
    &.high {
      .risk {
        color: #a80000;
      }
    }
  // '-1': '#128BED', // 提示
    &.alert {
      .risk {
        color: #128bed;
      }
    }
    // '-2': '#00AD65', // 良好
    &.pass {
      .risk {
        color: #00ad65;
      }
    }

  }
}
