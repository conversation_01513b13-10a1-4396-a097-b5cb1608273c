// Vitest Snapshot v1, https://vitest.dev/guide/snapshot.html

exports[`RiskTableNext > props: meta.key - BusinessAbnormal1 1`] = `
<div class="container">
  <div class="tableWrapper">
    <div class="table">
      <div class="container">
        <table class="table">
          <tbody></tbody>
        </table>
      </div>
    </div>
  </div>
</div>
`;

exports[`RiskTableNext > props: meta.key - BusinessAbnormal7 1`] = `
<div class="container">
  <div class="tableWrapper">
    <div>
      <div class="description">
        <div>DESCRIPTION</div>
      </div>
    </div>
  </div>
</div>
`;

exports[`RiskTableNext > props: meta.key - CompanyShell 1`] = `
<div class="container">
  <div class="tableWrapper">
    <div style="display: flex; flex-direction: column; gap: 15px;">
      <div>
        <div class="container">
          <div class="section">
            <div class="header">
              <div class="title">title</div>
              <div class="description">description</div>
            </div>
          </div>
          <div class="body"></div>
        </div>
      </div>
    </div>
  </div>
</div>
`;

exports[`RiskTableNext > props: meta.key - ContractBreach 1`] = `
<div class="container">
  <div class="tableWrapper">
    <div class="alertDanger contractBreach">
      <main>
        <div class="row">
          <div class="title">
            <q-icon-stub type="icon-icon_zishenfengxian" class="icon"></q-icon-stub><span>自身合同违约</span>
          </div>
          <div class="content">
            <div>
              <div class="cell"><span class="label">违约等级：</span><span class="value">-</span></div>
              <div class="cell"><span class="label">违约指数：</span><span class="value">-</span></div>
            </div>
            <div>
              <div class="cell"><span class="label">违约次数：</span><span class="value">-</span></div>
              <div class="cell"><span class="label">涉案金额：</span><span class="value">-</span></div>
            </div>
          </div>
        </div>
        <div class="row">
          <div class="title">
            <q-icon-stub type="icon-icon_zishenfengxian" class="icon"></q-icon-stub><span>关联方合同违约</span>
          </div>
          <div class="content">-</div>
        </div>
      </main>
      <aside><a href="/embed/bre-contract?keyNo=undefined&amp;title=undefined" target="_blank" class="detailButton">查看详情</a></aside>
    </div>
  </div>
</div>
`;

exports[`RiskTableNext > props: meta.key - Liquidation 1`] = `
<div class="container">
  <div class="tableWrapper"></div>
</div>
`;

exports[`RiskTableNext > props: meta.key - NegativeNewsRecent 1`] = `
<div class="container">
  <div class="tableWrapper">
    <div class="newsFeed table">
      <div class="loading">
        <q-loading size="fullsize"></q-loading>
      </div>
      <div class="list newsContent"><a href="/embed/post-news?newsId=undefined&amp;keyNo=undefined&amp;title=undefined" target="_blank" class="item">
          <header class="header">
            <h3></h3><span class="otherInfo"><span>-</span><span class="divider"></span><span>-</span></span>
          </header>
          <div class="tags"><span>#codedesc</span><span>#tagsnew</span></div>
        </a></div>
    </div>
  </div>
</div>
`;

exports[`RiskTableNext > props: meta.key - NoCapital 1`] = `
<div class="container">
  <div class="tableWrapper">
    <div class="table">
      <div class="container">
        <table class="table">
          <tbody>
            <tr>
              <div style="display: inline-flex; width: 50%;">
                <td width="155" class="tb"></td>
                <td style="flex-grow: 1; flex-shrink: 1; flex-basis: 0%; border-left-width: 0px; border-left-style: initial; border-left-color: initial; border-right-width: 0px; border-right-style: initial; border-right-color: initial;">-</td>
              </div>
              <div style="display: inline-flex; width: 50%;">
                <td width="155" class="tb"></td>
                <td style="flex-grow: 1; flex-shrink: 1; flex-basis: 0%; border-left-width: 0px; border-left-style: initial; border-left-color: initial;">-</td>
              </div>
            </tr>
          </tbody>
        </table>
      </div>
    </div>
  </div>
</div>
`;
