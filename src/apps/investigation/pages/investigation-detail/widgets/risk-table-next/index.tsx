/* istanbul ignore file */
import { Pagination } from 'ant-design-vue';
import { cloneDeep, isObject, omitBy, toArray, isNaN, isArray } from 'lodash';
import { computed, defineComponent, getCurrentInstance, nextTick, type PropType } from 'vue';

import QRichTable, { DEFAULT_PAGINATION_CONFIG } from '@/components/global/q-rich-table';
import { numberToHuman, numberToHumanWithUnit } from '@/utils/number-formatter';
import { scrollIntoView } from '@/hooks/use-scroll-to-view';
import { useDimensionDetail } from '@/hooks/use-dimenision-detail';
import QPlainTable from '@/components/global/q-plain-table';
import { useDynamicRiskDimensionDetail, dynamicDetailJudge } from '@/hooks/risk-dimension/use-dynamic-risk-dimension';

import { riskColumns } from '../../utils/risk-columns.config';
import { notDetailTypes, StringDetailTypes, JsonDetailTypes } from '../../utils/risk-type.config';
import { getScopedSlots } from './get-scoped-slots';
import styles from './risk-table-next.module.less';
import CompanyShellDimension from './widgets/company-shell-dimension';
import QfkEntityTableDimension from './widgets/qfk-entity-table-dimension';
import NegativeNewsDimension from './widgets/negative-news-dimension';

const CONTRACT_BREACH_RISK_LEVEL_MAP = {
  L5: 'L5/极高风险',
  L4: 'L4/高风险',
  L3: 'L3/中风险',
  L2: 'L2/低风险',
  L1: 'L1/极低风险',
};

const RiskTableNext = defineComponent({
  name: 'RiskTableNext',
  props: {
    hitDetail: {
      type: Object,
      default: () => ({}),
    },
    meta: {
      type: Object as PropType<Record<string, any>>,
      default: () => ({}) as Record<string, any>,
    },
    dataSource: {
      type: [Array, Object] as PropType<Record<string, any> | Record<string, any>[]>,
      required: false,
    },
    pagination: {
      type: Object as PropType<Record<string, any>>,
      default: () => ({}) as Record<string, any>,
    },
    sort: {
      type: Object as PropType<Record<string, any>>,
      required: false,
    },
    loading: {
      type: Boolean,
      default: true,
    },
    // 是否需要隐藏操作列
    hideAction: {
      type: Boolean,
      default: false,
    },
    rowKey: {
      type: String,
      default: 'Id',
    },
    // 同dimensionKey情况下的表格key
    displayKey: {
      type: String,
      default: '',
    },
  },
  setup(props, { emit }) {
    const vm: Record<string, any> = getCurrentInstance()?.proxy || {};

    const dimensionDesc = computed(() =>
      isArray(props.dataSource) && props.dataSource.length > 0 ? props.dataSource[0].dimensionDesc : ''
    );

    const totalHits = computed(() => props.pagination?.total || props.meta.totalHits);

    const { showDetail, goDetail, showInfo, gotoDetail } = useDimensionDetail(vm, props);

    const hideSubTitle = computed(() => {
      const dimensionName = `${props.hitDetail.name}${props.hitDetail.totalHits}`;
      const subTitle = `${props.meta.strategyName}${props.meta.totalHits}`;
      return dimensionName === subTitle;
    });

    const handleNestedDataSource = (data: Record<string, any>[]) => {
      const result: any[] = [];

      /**
       * 将类数组对象转化为数组
       * @param arrayLikeObject
       */
      const parseArrayLikeObject = (arrayLikeObject: Record<string, any>) => {
        return toArray(omitBy(arrayLikeObject, (val, key) => isNaN(Number(key))));
      };

      data.forEach((item) => {
        if (isObject(item)) {
          result.push(parseArrayLikeObject(item));
        } else {
          result.push(item);
        }
      });

      return result;
    };
    const [openDimensionDetail] = useDynamicRiskDimensionDetail();
    const handlePageChange = (current: number, pageSize: number) => {
      emit('pageChange', { current, pageSize });
      nextTick(() => {
        scrollIntoView(props.meta.key);
      });
    };
    const handleOrderChange = ({ sorter }) => {
      const ORDER_TYPE_MAP = {
        descend: 'DESC',
        ascend: 'ASC',
      };
      const order = ORDER_TYPE_MAP[sorter.order];
      const sort = {
        order,
        field: order ? sorter.columnKey : undefined,
        field2: order ? sorter.field || sorter.columnKey : undefined,
      };
      emit('sortChange', sort);
    };

    const openRiskTypeModal = (params) => {
      const enableTypes = {
        // 裁判文书
        Judgement: 'Judgement',
        // 严重违法
        SeriousViolation: 'CompanyCredit',
        // 经营异常
        Exception: 'OperationAbnormal',
        // 失信被执行人
        BadCreditExecuted: 'PersonCreditCurrent',
        // 税收违法
        TaxIllegal: 'TaxationOffences',
      };
      if (enableTypes[params.riskType]) {
        emit('openRiskTypeModal', {
          key: enableTypes[params.riskType],
          ids: params.vids,
          keyNo: params.keyNo,
          companyName: params.companyName,
          isInnerDetail: true,
          title: params.riskTypeDesc,
        });
      }
    };

    const getTableHeader = () => {
      if (dimensionDesc.value) {
        return dimensionDesc.value;
      }
      const descriptionMap = {
        OutwardInvestmentAnalysis: () => props.meta.description,
      };
      if (descriptionMap[props.meta.key]) {
        return descriptionMap[props.meta.key]();
      }
      return null;
    };

    return {
      hideSubTitle,
      totalHits,
      dimensionDesc,
      handleNestedDataSource,
      handleOrderChange,
      handlePageChange,
      gotoDetail,
      goDetail,
      showDetail,
      showInfo,
      openRiskTypeModal,
      openDimensionDetail,
      dynamicDetailJudge,
      getTableHeader,
    };
  },
  render() {
    const { dataSource, pagination, meta, loading, hitDetail } = this as any;

    const renderCompanyDetail = () => {
      const list = dataSource?.[0]?.description || [];
      // 法定代表人持股比例低
      if (hitDetail.name === '法定代表人持股比例低' || list[0]?.dimensionFieldKey === 'legalRepresentHoldingRatio') {
        return list.map((item) => {
          return (
            <div class={styles.table}>
              <QPlainTable>
                <tbody>
                  <tr>
                    <th width="155" class="tb">
                      法定代表人
                    </th>
                    <td>
                      <q-entity-link coy-obj={{ KeyNo: item.personKeyNo, Name: item.name }} />
                    </td>
                    <th width="155" class="tb">
                      总持股比例
                    </th>
                    <td>{item.stockPercent || '-'}</td>
                  </tr>
                </tbody>
              </QPlainTable>
            </div>
          );
        });
      }
      let desc = '';

      if (list.length === 1) {
        desc = list[0].desc;
      } else {
        const descArr = list.map((listObj: any) => {
          return `${listObj.hitDimensionFieldName}: ${listObj.hitDimensionFieldValue}`;
        });
        desc = `该企业${descArr.join('；')}`;
      }

      // 文本描述
      return <div class={styles.description}>{desc}</div>;
    };

    const renderNoDetailContent = () => {
      if (notDetailTypes.includes(meta.key) && dataSource) {
        // if (dataSource.length === 0) {
        //   return <QRichTableEmpty size="100px">暂无数据</QRichTableEmpty>;
        // }
        return (
          <div class={styles.tableWrapper}>
            {meta.isInnerDetail || this.hideSubTitle ? null : (
              <div class={styles.heading}>
                <div class={styles.left}>
                  <strong>{meta.strategyName}</strong>
                  <span>{this.totalHits}</span>
                  {this.$slots.extra}
                </div>
                {this.$slots.default}
              </div>
            )}

            {StringDetailTypes.includes(meta.key) ? (
              <div>
                <div class={styles.description}>
                  <div>{dataSource?.length ? dataSource[0].description : ''}</div>
                  {/* 【涉及高风险行业】 */}
                  {dataSource[0]?.scope ? <div>经营范围：{dataSource?.length ? dataSource[0].scope : ''}</div> : null}
                </div>

                {/* 异地经营 */}
                {dataSource?.[0]?.address || dataSource?.[0]?.address2 ? (
                  <div style={{ marginTop: '10px' }}>
                    <QPlainTable>
                      <tbody>
                        <tr>
                          <th width="155">企业注册地址</th>
                          <td>{dataSource[0].address || '-'}</td>
                        </tr>
                        <tr>
                          <th width="155">企业通讯地址</th>
                          <td>{dataSource[0].address2 || '-'}</td>
                        </tr>
                      </tbody>
                    </QPlainTable>
                  </div>
                ) : null}
              </div>
            ) : null}

            {/* 久无实缴 */}
            {['NoCapital'].includes(meta.key) ? (
              <div class={styles.table}>
                <QPlainTable>
                  <tbody>
                    {dataSource.map((item) => (
                      <tr>
                        <div style={{ display: 'inline-flex', width: '50%' }}>
                          <td width="155" class="tb">
                            {item.RegistCapiName}
                          </td>
                          <td style={{ flex: 1, borderLeft: 0, borderRight: 0 }}>{numberToHumanWithUnit(item.RegistCapiValue)}</td>
                        </div>

                        <div style={{ display: 'inline-flex', width: '50%' }}>
                          <td width="155" class="tb">
                            {item.RecCapName}
                          </td>
                          <td style={{ flex: 1, borderLeft: 0 }}>{numberToHumanWithUnit(item.RecCapValue)}</td>
                        </div>
                      </tr>
                    ))}
                  </tbody>
                </QPlainTable>
              </div>
            ) : null}

            {/* 实缴异常 */}
            {['RealCapitalException'].includes(meta.key) ? (
              <div class={styles.table}>
                <QPlainTable>
                  <tbody>
                    {dataSource.map((item) => (
                      <tr>
                        <div style={{ display: 'inline-flex', width: '33.33%' }}>
                          <td width="155" class="tb">
                            {item.RegistCapiName}
                          </td>
                          <td style={{ flex: 1, borderLeft: 0, borderRight: 0 }}>{numberToHumanWithUnit(item.RegistCapiValue)}</td>
                        </div>

                        <div style={{ display: 'inline-flex', width: '33.33%' }}>
                          <td width="155" class="tb">
                            {item.RecCapName}
                          </td>
                          <td style={{ flex: 1, borderLeft: 0, borderRight: 0 }}>{numberToHumanWithUnit(item.RecCapValue)}</td>
                        </div>

                        <div style={{ display: 'inline-flex', width: '33.33%' }}>
                          <td width="155" class="tb">
                            实缴比例
                          </td>
                          <td style={{ flex: 1, borderLeft: 0 }}>{item.Ratio ? `${Math.floor(item.Ratio * 10000) / 100}%` : '-'}</td>
                        </div>
                      </tr>
                    ))}
                  </tbody>
                </QPlainTable>
              </div>
            ) : null}

            {JsonDetailTypes.includes(meta.key) ? (
              <div class={styles.table}>
                <QPlainTable>
                  <tbody>
                    {dataSource?.length
                      ? dataSource.map((c: any, index) => {
                          return (
                            <tr key={`dataItem${index}`}>
                              <td width="155" class="tb">
                                {c.label}
                              </td>
                              <td style={{ flex: 1, borderLeft: 0 }}>{c.value}</td>
                            </tr>
                          );
                        })
                      : null}
                  </tbody>
                </QPlainTable>
              </div>
            ) : null}

            {meta.key === 'CompanyShell' ? (
              <div
                style={{
                  display: 'flex',
                  flexDirection: 'column',
                  gap: '15px',
                }}
              >
                {/* EmptyShell */}
                {dataSource.map((item, index) => {
                  return (
                    <div key={item.Id}>
                      <CompanyShellDimension dataSource={item} index={dataSource.length > 1 ? index : undefined} />
                    </div>
                  );
                })}
              </div>
            ) : null}

            {/* 合同违约: 乐视网信息技术（北京）股份有限公司 */}
            {meta.key === 'ContractBreach' ? (
              <div class={[styles.alertDanger, styles.contractBreach]}>
                <main>
                  <div class={styles.row}>
                    <div class={styles.title}>
                      <q-icon type="icon-icon_zishenfengxian" class={styles.icon} />
                      <span>自身合同违约</span>
                    </div>
                    <div class={styles.content}>
                      <div>
                        <div class={styles.cell}>
                          <span class={styles.label}>违约等级：</span>
                          <span class={styles.value}>{CONTRACT_BREACH_RISK_LEVEL_MAP[dataSource[0]?.Revel] || '-'}</span>
                        </div>
                        <div class={styles.cell}>
                          <span class={styles.label}>违约指数：</span>
                          <span class={styles.value}>{dataSource[0]?.ScoreLog || '-'}</span>
                        </div>
                      </div>
                      <div>
                        <div class={styles.cell}>
                          <span class={styles.label}>违约次数：</span>
                          <span class={styles.value}>{dataSource[0]?.TotalNum || '-'}</span>
                        </div>
                        <div class={styles.cell}>
                          <span class={styles.label}>涉案金额：</span>
                          <span class={styles.value}>
                            {dataSource[0]?.TotalAmt && dataSource[0]?.TotalAmt !== '0'
                              ? `${numberToHuman(dataSource[0]?.TotalAmt / 10000, {
                                  precision: 2,
                                })}万元`
                              : '-'}
                          </span>
                        </div>
                      </div>
                    </div>
                  </div>
                  <div class={styles.row}>
                    <div class={styles.title}>
                      <q-icon type="icon-icon_zishenfengxian" class={styles.icon} />
                      <span>关联方合同违约</span>
                    </div>
                    <div class={styles.content}>
                      {/* 返回相关风险数组件按照由高到低顺序排列，长度大于等于2才展示（first: 展示最高级别的相关风险，last：相关风险总数） */}
                      {Array.isArray(dataSource[0]?.Relate) && dataSource[0]?.Relate.length > 1 ? (
                        <div>
                          <span>可计算出合同违约指数的关联方共</span>
                          <em>{dataSource[0]?.Relate[dataSource[0]?.Relate.length - 1]?.Count}</em>
                          <span>个，</span>
                          <span>{dataSource[0]?.Relate[0]?.Desc}的关联方</span>
                          <em>{dataSource[0]?.Relate[0]?.Count}</em>
                          <span>个</span>

                          <a href={`/embed/bre-contract?keyNo=${meta.keyNo}&activeIndex=1&title=${meta.companyName}`} target="_blank">
                            详情
                          </a>
                        </div>
                      ) : (
                        '-'
                      )}
                    </div>
                  </div>
                </main>
                <aside>
                  <a href={`/embed/bre-contract?keyNo=${meta.keyNo}&title=${meta.companyName}`} class={styles.detailButton} target="_blank">
                    查看详情
                  </a>
                </aside>
              </div>
            ) : null}

            {/* 近三年负面新闻: 中国电子科技集团有限公司 */}
            {['NegativeNews', 'NegativeNewsRecent', 'NegativeNewsHistory'].includes(meta.key) ? (
              <div
                class={{
                  [styles.newsFeed]: true,
                  [styles.table]: true,
                }}
              >
                <div class={styles.loading} v-show={loading}>
                  <q-loading size="fullsize" />
                </div>
                <NegativeNewsDimension class={styles.newsContent} dataSource={dataSource} meta={meta} rowKey={this.rowKey} />

                {/* 数量超过当前页数量的时候显示分页 */}
                {pagination.total > pagination.pageSize && (
                  <div class={styles.pagination}>
                    <Pagination
                      {...{
                        props: {
                          ...DEFAULT_PAGINATION_CONFIG,
                          ...pagination,
                          pageSizeOptions: ['5', '10'],
                        },
                        on: {
                          change: this.handlePageChange,
                          showSizeChange: this.handlePageChange,
                        },
                      }}
                    />
                  </div>
                )}
              </div>
            ) : null}

            {/* 清算信息 */}
            {['Liquidation'].includes(meta.key) && dataSource?.length > 0 ? (
              <div class={styles.table}>
                <QPlainTable>
                  {/* <colgroup>
                    <col width="100" />
                  </colgroup> */}
                  <tbody>
                    {dataSource.map(({ Id, Leader, Member }: Record<string, any> = {}) => {
                      return (
                        <tr key={Id}>
                          <th width="23%">清算组负责人</th>
                          <td width="27%">{Leader || '-'}</td>
                          <th width="23%">清算组成员</th>
                          <td width="27%">{Member || '-'}</td>
                        </tr>
                      );
                    })}
                  </tbody>
                </QPlainTable>
              </div>
            ) : null}
            {['CompanyDetail'].includes(meta.key) ? renderCompanyDetail() : null}
            {/* 所有权与经营权分离 */}
            {['QfkRisk6802'].includes(meta.key) && dataSource?.length > 0 ? <QfkEntityTableDimension dataSource={dataSource} /> : null}
            {/* 实际控制人无法识别或穿透边界以外 */}
            {/* 员工数据不明 */}
            {['QfkRisk6615', 'QfkRisk6302'].includes(meta.key) && dataSource?.length > 0 ? (
              <div class={styles.description}>{dataSource?.length ? dataSource[0].dimensionDesc : ''}</div>
            ) : null}
            {/* 企查分 */}
            {['QCCCreditRate'].includes(meta.key) && dataSource?.length > 0 ? (
              <div class={styles.description}>
                目标企业企查分{dataSource[0].Score}分，{dataSource[0].ScoreDescInfo}
              </div>
            ) : null}
          </div>
        );
      }
      // if (notDetailTypes.includes(meta.key)) {
      //   return (
      //     <div style="height: 160px">
      //       <QLoading size="fullsize" />
      //     </div>
      //   );
      // }
      return null;
    };
    const formColumns = () => {
      // 对于dimensionkey一样的维度，需要通过取displayKey确定cloneColumns
      const dimensionKey = this.displayKey || meta.subDimensionKey || meta.key;
      const cloneColumns = cloneDeep(riskColumns[dimensionKey]);
      return cloneColumns;
    };
    const tableColumnConfig = formColumns();

    // 【控制权分散】有些数据没有表格，直接展示文字描述
    const isQfkRisk6803TableVisible = meta.key === 'QfkRisk6803' && dataSource?.[0]?.name;
    const isTableVisible = !['QfkRisk6803'].includes(meta.key) || isQfkRisk6803TableVisible;

    return (
      <div class={styles.container}>
        {/* 无详情的内容 */}
        {renderNoDetailContent()}

        {!notDetailTypes.includes(meta.key) && Array.isArray(dataSource) ? (
          <div class={styles.tableWrapper}>
            {meta.isInnerDetail || this.hideSubTitle ? null : (
              <div class={styles.heading}>
                <div class={styles.left}>
                  <strong>{meta.strategyName}</strong>
                  <span>{this.totalHits}</span>
                  {this.$slots.extra}
                </div>
                {this.$slots.default}
              </div>
            )}
            {this.getTableHeader() ? <div class={styles.description}>{this.getTableHeader()}</div> : null}
            <div class={styles.table}>
              <QRichTable
                v-show={isTableVisible}
                key={this.meta.tkey}
                rowKey={this.rowKey}
                headerAlign={'left'}
                loading={loading}
                columns={tableColumnConfig}
                dataSource={dataSource || []}
                onChange={this.handleOrderChange}
                showIndex={!['ContractBreach', 'JudicialCase'].includes(meta.key)}
                pagination={{
                  ...pagination,
                  pageSizeOptions: ['5', '10'],
                  onChange: this.handlePageChange,
                  onShowSizeChange: this.handlePageChange,
                }}
                scopedSlots={getScopedSlots({ ...meta })}
              />
            </div>
          </div>
        ) : null}
      </div>
    );
  },
});

export default RiskTableNext;
