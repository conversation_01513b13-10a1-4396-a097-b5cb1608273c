// Vitest Snapshot v1, https://vitest.dev/guide/snapshot.html

exports[`CompanyShellDimension > dataSource为空对象时不展示任何内容 1`] = `
<div class="container">
  <div class="section">
    <div class="header">
      <div class="title"></div>
      <div class="description"></div>
    </div>
  </div>
  <div class="body"></div>
</div>
`;

exports[`CompanyShellDimension > 展示: 一人多企 1`] = `
<div class="container">
  <div class="section">
    <div class="header">
      <div class="title">其他维度</div>
      <div class="description">其他维度信息</div>
    </div>
  </div>
  <div class="body">
    <div class="container">
      <div class="ant-spin-nested-loading">
        <div class="ant-spin-container">
          <div class="ant-table-wrapper table scrollable" emptytext="暂时没有找到相关数据" scrolltofirstrowonchange="true">
            <div class="ant-spin-nested-loading">
              <div class="ant-spin-container">
                <div class="ant-table ant-table-scroll-position-left ant-table-default ant-table-bordered">
                  <div class="ant-table-content">
                    <!---->
                    <div class="ant-table-body">
                      <table class="">
                        <colgroup>
                          <col style="width: 58px; min-width: 58px;">
                        </colgroup>
                        <thead class="ant-table-thead">
                          <tr>
                            <th key="0" align="left" class="ant-table-align-left ant-table-row-cell-break-word ant-table-row-cell-last" style="text-align: left;"><span class="ant-table-header-column"><div><span class="ant-table-column-title">序号</span><span class="ant-table-column-sorter"></span>
                    </div></span></th>
                    </tr>
                    </thead>
                    <tbody class="ant-table-tbody">
                      <tr class="ant-table-row ant-table-row-level-0" data-row-key="0">
                        <td rowspan="1" style="position: relative; text-align: left;" dataindex="0" class="ant-table-row-cell-break-word"><span>1</span></td>
                      </tr>
                      <tr class="ant-table-row ant-table-row-level-0" data-row-key="1">
                        <td rowspan="1" style="position: relative; text-align: left;" dataindex="1" class="ant-table-row-cell-break-word"><span>2</span></td>
                      </tr>
                    </tbody>
                    </table>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</div>
</div>
`;

exports[`CompanyShellDimension > 展示: 一址多企 1`] = `
<div class="container">
  <div class="section">
    <div class="header">
      <div class="title">一址多企</div>
      <div class="description">一址多企信息</div>
    </div>
  </div>
  <div class="body"></div>
</div>
`;

exports[`CompanyShellDimension > 展示: 企业自然人变更时间集中 1`] = `
<div class="container">
  <div class="section">
    <div class="header">
      <div class="title">企业自然人变更时间集中</div>
      <div class="description">企业自然人变更时间集中</div>
    </div>
  </div>
  <div class="body">
    <div>
      <div class="subTitle">历史股东</div>
      <div class="container">
        <div class="ant-spin-nested-loading">
          <div class="ant-spin-container">
            <div class="ant-table-wrapper table scrollable" emptytext="暂时没有找到相关数据" scrolltofirstrowonchange="true">
              <div class="ant-spin-nested-loading">
                <div class="ant-spin-container">
                  <div class="ant-table ant-table-scroll-position-left ant-table-default ant-table-bordered">
                    <div class="ant-table-content">
                      <!---->
                      <div class="ant-table-body">
                        <table class="">
                          <colgroup>
                            <col style="width: 58px; min-width: 58px;">
                            <col style="width: 240px; min-width: 240px;">
                            <col>
                            <col>
                            <col style="width: 150px; min-width: 150px;">
                            <col style="width: 150px; min-width: 150px;">
                          </colgroup>
                          <thead class="ant-table-thead">
                            <tr>
                              <th key="0" align="left" class="ant-table-align-left ant-table-row-cell-break-word" style="text-align: left;"><span class="ant-table-header-column"><div><span class="ant-table-column-title">序号</span><span class="ant-table-column-sorter"></span>
                      </div></span></th>
                      <th key="1" align="left" class="ant-table-row-cell-break-word"><span class="ant-table-header-column"><div><span class="ant-table-column-title">股东</span><span class="ant-table-column-sorter"></span>
                    </div></span></th>
                    <th key="stockPercent" align="left" class=""><span class="ant-table-header-column"><div><span class="ant-table-column-title">持股比例</span><span class="ant-table-column-sorter"></span>
                  </div></span></th>
                  <th key="type" align="left" class=""><span class="ant-table-header-column"><div><span class="ant-table-column-title">股东类型</span><span class="ant-table-column-sorter"></span>
                </div></span></th>
                <th key="inDate" align="left" class="ant-table-row-cell-break-word"><span class="ant-table-header-column"><div><span class="ant-table-column-title">参股日期</span><span class="ant-table-column-sorter"></span>
              </div></span></th>
              <th key="changeDate" align="left" class="ant-table-row-cell-break-word ant-table-row-cell-last"><span class="ant-table-header-column"><div><span class="ant-table-column-title">退出日期</span><span class="ant-table-column-sorter"></span>
            </div></span></th>
            </tr>
            </thead>
            <tbody class="ant-table-tbody">
              <tr class="ant-table-row ant-table-row-level-0" data-row-key="0">
                <td rowspan="1" style="position: relative; text-align: left;" dataindex="0" class="ant-table-row-cell-break-word"><span>1</span></td>
                <td class="ant-table-row-cell-break-word"><span class="container"><span class="content ellipsis"><a style="display: inline" href="/embed/companyDetail?keyNo=abc&amp;title=李四" target="_blank" class="container undefined"><span class="name">李四</span></a><span class="extra" style="display: none;"></span></span></span></td>
                <td class="">99%</td>
                <td class="">大股东</td>
                <td class="ant-table-row-cell-break-word">2021-02-01</td>
                <td class="ant-table-row-cell-break-word">2021-01-01</td>
              </tr>
            </tbody>
            </table>
          </div>
        </div>
      </div>
    </div>
  </div>
</div>
</div>
</div>
</div>
</div>
<div>
  <div class="subTitle">历史高管</div>
  <div class="container">
    <div class="ant-spin-nested-loading">
      <div class="ant-spin-container">
        <div class="ant-table-wrapper table scrollable" emptytext="暂时没有找到相关数据" scrolltofirstrowonchange="true">
          <div class="ant-spin-nested-loading">
            <div class="ant-spin-container">
              <div class="ant-table ant-table-scroll-position-left ant-table-default ant-table-bordered">
                <div class="ant-table-content">
                  <!---->
                  <div class="ant-table-body">
                    <table class="">
                      <colgroup>
                        <col style="width: 58px; min-width: 58px;">
                        <col style="width: 240px; min-width: 240px;">
                        <col>
                        <col style="width: 150px; min-width: 150px;">
                        <col style="width: 150px; min-width: 150px;">
                      </colgroup>
                      <thead class="ant-table-thead">
                        <tr>
                          <th key="0" align="left" class="ant-table-align-left ant-table-row-cell-break-word" style="text-align: left;"><span class="ant-table-header-column"><div><span class="ant-table-column-title">序号</span><span class="ant-table-column-sorter"></span>
                  </div></span></th>
                  <th key="1" align="left" class="ant-table-row-cell-break-word"><span class="ant-table-header-column"><div><span class="ant-table-column-title">姓名</span><span class="ant-table-column-sorter"></span>
                </div></span></th>
                <th key="job" align="left" class=""><span class="ant-table-header-column"><div><span class="ant-table-column-title">职务</span><span class="ant-table-column-sorter"></span>
              </div></span></th>
              <th key="inDate" align="left" class="ant-table-row-cell-break-word"><span class="ant-table-header-column"><div><span class="ant-table-column-title">任职日期</span><span class="ant-table-column-sorter"></span>
            </div></span></th>
            <th key="changeDate" align="left" class="ant-table-row-cell-break-word ant-table-row-cell-last"><span class="ant-table-header-column"><div><span class="ant-table-column-title">卸职日期</span><span class="ant-table-column-sorter"></span>
          </div></span></th>
          </tr>
          </thead>
          <tbody class="ant-table-tbody">
            <tr class="ant-table-row ant-table-row-level-0" data-row-key="0">
              <td rowspan="1" style="position: relative; text-align: left;" dataindex="0" class="ant-table-row-cell-break-word"><span>1</span></td>
              <td class="ant-table-row-cell-break-word"><span class="container"><span class="content ellipsis"><a style="display: inline" href="/embed/companyDetail?keyNo=abc&amp;title=李四" target="_blank" class="container undefined"><span class="name">李四</span></a><span class="extra" style="display: none;"></span></span></span></td>
              <td class="">董事</td>
              <td class="ant-table-row-cell-break-word">2021-02-01</td>
              <td class="ant-table-row-cell-break-word">2021-01-01</td>
            </tr>
          </tbody>
          </table>
        </div>
      </div>
    </div>
  </div>
</div>
</div>
</div>
</div>
</div>
</div>
<div>
  <div class="subTitle">历史法定代表人</div>
  <div class="container">
    <div class="ant-spin-nested-loading">
      <div class="ant-spin-container">
        <div class="ant-table-wrapper table scrollable" emptytext="暂时没有找到相关数据" scrolltofirstrowonchange="true">
          <div class="ant-spin-nested-loading">
            <div class="ant-spin-container">
              <div class="ant-table ant-table-scroll-position-left ant-table-default ant-table-bordered">
                <div class="ant-table-content">
                  <!---->
                  <div class="ant-table-body">
                    <table class="">
                      <colgroup>
                        <col style="width: 58px; min-width: 58px;">
                        <col>
                        <col style="width: 150px; min-width: 150px;">
                        <col style="width: 150px; min-width: 150px;">
                      </colgroup>
                      <thead class="ant-table-thead">
                        <tr>
                          <th key="0" align="left" class="ant-table-align-left ant-table-row-cell-break-word" style="text-align: left;"><span class="ant-table-header-column"><div><span class="ant-table-column-title">序号</span><span class="ant-table-column-sorter"></span>
                  </div></span></th>
                  <th key="1" align="left" class=""><span class="ant-table-header-column"><div><span class="ant-table-column-title">姓名</span><span class="ant-table-column-sorter"></span>
                </div></span></th>
                <th key="inDate" align="left" class="ant-table-row-cell-break-word"><span class="ant-table-header-column"><div><span class="ant-table-column-title">任职日期</span><span class="ant-table-column-sorter"></span>
              </div></span></th>
              <th key="changeDate" align="left" class="ant-table-row-cell-break-word ant-table-row-cell-last"><span class="ant-table-header-column"><div><span class="ant-table-column-title">卸任日期</span><span class="ant-table-column-sorter"></span>
            </div></span></th>
            </tr>
            </thead>
            <tbody class="ant-table-tbody">
              <tr class="ant-table-row ant-table-row-level-0" data-row-key="0">
                <td rowspan="1" style="position: relative; text-align: left;" dataindex="0" class="ant-table-row-cell-break-word"><span>1</span></td>
                <td class=""><span class="container"><span class="content ellipsis"><a style="display: inline" href="/embed/companyDetail?keyNo=abc&amp;title=李四" target="_blank" class="container undefined"><span class="name">李四</span></a><span class="extra" style="display: none;"></span></span></span></td>
                <td class="ant-table-row-cell-break-word">2021-02-01</td>
                <td class="ant-table-row-cell-break-word">2021-01-01</td>
              </tr>
            </tbody>
            </table>
          </div>
        </div>
      </div>
    </div>
  </div>
</div>
</div>
</div>
</div>
</div>
</div>
</div>
`;

exports[`CompanyShellDimension > 展示: 无法联系该企业 1`] = `
<div class="container">
  <div class="section">
    <div class="header">
      <div class="title">无法联系该企业</div>
      <div class="description">企业信息无法联系</div>
    </div>
  </div>
  <div class="body">
    <div class="container">
      <div class="ant-spin-nested-loading">
        <div class="ant-spin-container">
          <div class="ant-table-wrapper table scrollable" emptytext="暂时没有找到相关数据" scrolltofirstrowonchange="true">
            <div class="ant-spin-nested-loading">
              <div class="ant-spin-container">
                <div class="ant-table ant-table-scroll-position-left ant-table-default ant-table-bordered">
                  <div class="ant-table-content">
                    <!---->
                    <div class="ant-table-body">
                      <table class="">
                        <colgroup>
                          <col style="width: 58px; min-width: 58px;">
                          <col style="width: 260px; min-width: 260px;">
                          <col>
                          <col style="width: 431px; min-width: 431px;">
                        </colgroup>
                        <thead class="ant-table-thead">
                          <tr>
                            <th key="0" align="left" class="ant-table-align-left ant-table-row-cell-break-word" style="text-align: left;"><span class="ant-table-header-column"><div><span class="ant-table-column-title">序号</span><span class="ant-table-column-sorter"></span>
                    </div></span></th>
                    <th key="addDate" align="left" class="ant-table-row-cell-break-word"><span class="ant-table-header-column"><div><span class="ant-table-column-title">列入日期</span><span class="ant-table-column-sorter"></span>
                  </div></span></th>
                  <th key="decisionOffice" align="left" class=""><span class="ant-table-header-column"><div><span class="ant-table-column-title">作出决定机关</span><span class="ant-table-column-sorter"></span>
                </div></span></th>
                <th key="addReason" align="left" class="ant-table-row-cell-break-word ant-table-row-cell-last"><span class="ant-table-header-column"><div><span class="ant-table-column-title">列入经营异常名录原因</span><span class="ant-table-column-sorter"></span>
              </div></span></th>
              </tr>
              </thead>
              <tbody class="ant-table-tbody">
                <tr class="ant-table-row ant-table-row-level-0" data-row-key="0">
                  <td rowspan="1" style="position: relative; text-align: left;" dataindex="0" class="ant-table-row-cell-break-word"><span>1</span></td>
                  <td class="ant-table-row-cell-break-word">2021-01-01</td>
                  <td class="">北京市海淀区人民法院</td>
                  <td class="ant-table-row-cell-break-word">无法联系该企业</td>
                </tr>
              </tbody>
              </table>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</div>
</div>
</div>
</div>
</div>
`;

exports[`CompanyShellDimension > 展示: 未公示年报 1`] = `
<div class="container">
  <div class="section">
    <div class="header">
      <div class="title">无法联系该企业</div>
      <div class="description">企业信息无法联系</div>
    </div>
  </div>
  <div class="body">
    <div class="container">
      <div class="ant-spin-nested-loading">
        <div class="ant-spin-container">
          <div class="ant-table-wrapper table scrollable" emptytext="暂时没有找到相关数据" scrolltofirstrowonchange="true">
            <div class="ant-spin-nested-loading">
              <div class="ant-spin-container">
                <div class="ant-table ant-table-scroll-position-left ant-table-default ant-table-bordered">
                  <div class="ant-table-content">
                    <!---->
                    <div class="ant-table-body">
                      <table class="">
                        <colgroup>
                          <col style="width: 58px; min-width: 58px;">
                          <col style="width: 260px; min-width: 260px;">
                          <col>
                          <col style="width: 431px; min-width: 431px;">
                        </colgroup>
                        <thead class="ant-table-thead">
                          <tr>
                            <th key="0" align="left" class="ant-table-align-left ant-table-row-cell-break-word" style="text-align: left;"><span class="ant-table-header-column"><div><span class="ant-table-column-title">序号</span><span class="ant-table-column-sorter"></span>
                    </div></span></th>
                    <th key="addDate" align="left" class="ant-table-row-cell-break-word"><span class="ant-table-header-column"><div><span class="ant-table-column-title">列入日期</span><span class="ant-table-column-sorter"></span>
                  </div></span></th>
                  <th key="decisionOffice" align="left" class=""><span class="ant-table-header-column"><div><span class="ant-table-column-title">作出决定机关</span><span class="ant-table-column-sorter"></span>
                </div></span></th>
                <th key="addReason" align="left" class="ant-table-row-cell-break-word ant-table-row-cell-last"><span class="ant-table-header-column"><div><span class="ant-table-column-title">列入经营异常名录原因</span><span class="ant-table-column-sorter"></span>
              </div></span></th>
              </tr>
              </thead>
              <tbody class="ant-table-tbody">
                <tr class="ant-table-row ant-table-row-level-0" data-row-key="0">
                  <td rowspan="1" style="position: relative; text-align: left;" dataindex="0" class="ant-table-row-cell-break-word"><span>1</span></td>
                  <td class="ant-table-row-cell-break-word">2021-01-01</td>
                  <td class="">北京市海淀区人民法院</td>
                  <td class="ant-table-row-cell-break-word">无法联系该企业</td>
                </tr>
              </tbody>
              </table>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</div>
</div>
</div>
</div>
</div>
`;

exports[`CompanyShellDimension > 展示: 注册信息相似度过高 1`] = `
<div class="container">
  <div class="section">
    <div class="header">
      <div class="title">其他维度</div>
      <div class="description">其他维度信息</div>
    </div>
  </div>
  <div class="body">
    <div class="container">
      <div class="ant-spin-nested-loading">
        <div class="ant-spin-container">
          <div class="ant-table-wrapper table scrollable" emptytext="暂时没有找到相关数据" scrolltofirstrowonchange="true">
            <div class="ant-spin-nested-loading">
              <div class="ant-spin-container">
                <div class="ant-table ant-table-scroll-position-left ant-table-default ant-table-bordered">
                  <div class="ant-table-content">
                    <!---->
                    <div class="ant-table-body">
                      <table class="">
                        <colgroup>
                          <col style="width: 58px; min-width: 58px;">
                        </colgroup>
                        <thead class="ant-table-thead">
                          <tr>
                            <th key="0" align="left" class="ant-table-align-left ant-table-row-cell-break-word ant-table-row-cell-last" style="text-align: left;"><span class="ant-table-header-column"><div><span class="ant-table-column-title">序号</span><span class="ant-table-column-sorter"></span>
                    </div></span></th>
                    </tr>
                    </thead>
                    <tbody class="ant-table-tbody">
                      <tr class="ant-table-row ant-table-row-level-0" data-row-key="0">
                        <td rowspan="1" style="position: relative; text-align: left;" dataindex="0" class="ant-table-row-cell-break-word"><span>1</span></td>
                      </tr>
                      <tr class="ant-table-row ant-table-row-level-0" data-row-key="1">
                        <td rowspan="1" style="position: relative; text-align: left;" dataindex="1" class="ant-table-row-cell-break-word"><span>2</span></td>
                      </tr>
                    </tbody>
                    </table>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</div>
</div>
`;

exports[`CompanyShellDimension > 展示: 注册基础信息重叠 1`] = `
<div class="container">
  <div class="section">
    <div class="header">
      <div class="title">其他维度</div>
      <div class="description">其他维度信息</div>
    </div>
  </div>
  <div class="body">
    <div class="container">
      <div class="ant-spin-nested-loading">
        <div class="ant-spin-container">
          <div class="ant-table-wrapper table scrollable" emptytext="暂时没有找到相关数据" scrolltofirstrowonchange="true">
            <div class="ant-spin-nested-loading">
              <div class="ant-spin-container">
                <div class="ant-table ant-table-scroll-position-left ant-table-default ant-table-bordered">
                  <div class="ant-table-content">
                    <!---->
                    <div class="ant-table-body">
                      <table class="">
                        <colgroup>
                          <col style="width: 58px; min-width: 58px;">
                        </colgroup>
                        <thead class="ant-table-thead">
                          <tr>
                            <th key="0" align="left" class="ant-table-align-left ant-table-row-cell-break-word ant-table-row-cell-last" style="text-align: left;"><span class="ant-table-header-column"><div><span class="ant-table-column-title">序号</span><span class="ant-table-column-sorter"></span>
                    </div></span></th>
                    </tr>
                    </thead>
                    <tbody class="ant-table-tbody">
                      <tr class="ant-table-row ant-table-row-level-0" data-row-key="0">
                        <td rowspan="1" style="position: relative; text-align: left;" dataindex="0" class="ant-table-row-cell-break-word"><span>1</span></td>
                      </tr>
                      <tr class="ant-table-row ant-table-row-level-0" data-row-key="1">
                        <td rowspan="1" style="position: relative; text-align: left;" dataindex="1" class="ant-table-row-cell-break-word"><span>2</span></td>
                      </tr>
                    </tbody>
                    </table>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</div>
</div>
`;
