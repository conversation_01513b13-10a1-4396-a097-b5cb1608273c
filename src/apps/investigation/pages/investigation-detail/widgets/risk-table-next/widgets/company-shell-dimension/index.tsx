import { defineComponent } from 'vue';

import VirtualPaginationTable from '../virtual-pagination-table';
import styles from './company-shell-dimension.module.less';
import { DIMENSION_COLUMN_MAP, SUB_DIMENSION_NAME } from './company-shell-dimension.config';

const CompanyShellDimension = defineComponent({
  name: 'CompanyShellDimension',
  props: {
    dataSource: {
      type: Object,
      default: () => ({}),
    },
    index: {
      type: Number,
      required: false,
    },
  },
  render() {
    return (
      <div class={styles.container}>
        <div class={[styles.section]}>
          <div class={styles.header}>
            <div class={styles.title}>{this.dataSource.title}</div>
            <div class={styles.description}>{this.dataSource.description}</div>
          </div>
        </div>
        {/* <div>{this.dataSource.hasDetail}</div> */}
        <div class={styles.body}>
          {/*  Object: dataSource.data.result */}
          {this.dataSource.hasDetail === '1' && this.dataSource.title === '无法联系该企业' ? (
            <VirtualPaginationTable
              pagination={{
                pageSize: 5,
                pageSizeOptions: ['5', '10'],
              }}
              rowKey={(record, index) => {
                return index;
              }}
              dataSource={this.dataSource.data?.result ? [this.dataSource.data.result] : []}
              columns={DIMENSION_COLUMN_MAP[this.dataSource.title]}
            />
          ) : null}

          {this.dataSource.hasDetail === '1' && this.dataSource.title === '企业自然人变更时间集中'
            ? Object.keys(this.dataSource.data?.result || {}).map((key) => {
                return (
                  <div>
                    <div class={styles.subTitle}>{SUB_DIMENSION_NAME[key]}</div>
                    <VirtualPaginationTable
                      key={key}
                      pagination={{
                        pageSize: 5,
                        pageSizeOptions: ['5', '10'],
                      }}
                      rowKey={(record, index) => {
                        return index;
                      }}
                      dataSource={this.dataSource.data.result[key]}
                      columns={DIMENSION_COLUMN_MAP[key]}
                    />
                  </div>
                );
              })
            : null}

          {/* List: dataSource.data.resultList */}
          {this.dataSource.hasDetail === '1' &&
          Array.isArray(this.dataSource.data?.resultList) &&
          this.dataSource.data.resultList.length ? (
            <VirtualPaginationTable
              pagination={{
                pageSize: 5,
                pageSizeOptions: ['5', '10'],
              }}
              // rowKey={'reKeyNo'}
              rowKey={(record, index) => {
                return index;
              }}
              dataSource={this.dataSource.data.resultList}
              columns={DIMENSION_COLUMN_MAP[this.dataSource.title]}
            />
          ) : null}
        </div>
      </div>
    );
  },
});

export default CompanyShellDimension;
