import { defineComponent, PropType } from 'vue';

import QEntityLink from '@/components/global/q-entity-link';
import { dateFormat } from '@/utils/format';

import styles from './qfk-entity-table-dimension.module.less';
import VirtualPaginationTable from '../virtual-pagination-table';

const QFK_PARTNER_LIST_COLUMNS = [
  {
    title: '股东',
    dataIndex: 'stockName',
    width: 220,
    scopedSlots: { customRender: 'partnerEntityLink' },
  },
  {
    title: '持股比例',
    dataIndex: 'stockPercent',
  },
  {
    title: '认缴出资额(万元)',
    dataIndex: 'shouldCapi',
  },
  {
    title: '认缴出资日期',
    dataIndex: 'shoudDate',
    scopedSlots: { customRender: 'date' },
  },
];

const QFK_EMPLOYEE_LIST_COLUMNS = [
  {
    title: '姓名',
    dataIndex: 'name',
    width: 220,
    scopedSlots: { customRender: 'employeeEntityLink' },
  },
  {
    title: '职务',
    dataIndex: 'job',
  },
];

const TABLE_SCOPED_SLOTS = {
  partnerEntityLink: (_, record) => (
    <QEntityLink
      coyObj={{
        Name: record.stockName,
        KeyNo: record.keyNo,
      }}
    />
  ),
  employeeEntityLink: (_, record) => (
    <QEntityLink
      coyObj={{
        Name: record.name,
        KeyNo: record.keyNo,
      }}
    />
  ),
  date: (text) => dateFormat(text),
};

const TABLE_PAGINATION_CONFIG = {
  pageSize: 5,
  pageSizeOptions: ['5', '10'],
};

const QfkEntityTableDimension = defineComponent({
  name: 'QfkEntityTableDimension',
  props: {
    dataSource: {
      type: Array as PropType<any[]>,
      default: () => [],
    },
  },
  render() {
    return (
      <div class={styles.container}>
        {this.dataSource.map((item) => {
          return (
            <div key={item.recordId} class={styles.list}>
              <div v-show={item.dimensionDesc} class={styles.dimensionDescription}>
                <header class={styles.header}>
                  <div class={styles.title}>{item.dimensionDesc}</div>
                </header>
              </div>
              <div v-show={item.partnerList.length > 0} class={styles.block}>
                <header class={styles.header}>
                  <div class={styles.title}>股东信息</div>
                </header>
                <VirtualPaginationTable
                  pagination={TABLE_PAGINATION_CONFIG}
                  rowKey={'keyNo'}
                  dataSource={item.partnerList}
                  columns={QFK_PARTNER_LIST_COLUMNS}
                  scopedSlots={TABLE_SCOPED_SLOTS}
                />
              </div>
              <div v-show={item.employeeList.length > 0} class={styles.block}>
                <header class={styles.header}>
                  <div class={styles.title}>主要人员</div>
                </header>
                <VirtualPaginationTable
                  pagination={TABLE_PAGINATION_CONFIG}
                  rowKey={'keyNo'}
                  dataSource={item.employeeList}
                  columns={QFK_EMPLOYEE_LIST_COLUMNS}
                  scopedSlots={TABLE_SCOPED_SLOTS}
                />
              </div>
            </div>
          );
        })}
      </div>
    );
  },
});

export default QfkEntityTableDimension;
