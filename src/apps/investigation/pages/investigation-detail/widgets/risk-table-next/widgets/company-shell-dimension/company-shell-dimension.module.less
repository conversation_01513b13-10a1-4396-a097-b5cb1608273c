.container {
  display: flex;
  flex-direction: column;

  .section {
    &.plainText {
      border: 1px solid #e4eef6;
    }
  }


  .header {
    .title {
      font-weight: 500;
      display: flex;
      justify-content: space-between;
      align-items: center;
      height: 40px;
      position: relative;
      padding-left: 12px;

      &::before {
        content: '';
        position: absolute;
        width: 4px;
        height: 16px;
        border-radius: 2px;
        background: #128BED;
        top: 50%;
        left: 0;
        transform: translateY(-50%);
      }
    }

    .description {
      color: #666;
      line-height: 22px;
      padding: 4px 0;
    }
  }

  .subTitle {
    font-weight: 500;
    font-size: 13px;
    padding: 3px 0;
  }
}
