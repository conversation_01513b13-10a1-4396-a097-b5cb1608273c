// Vitest Snapshot v1, https://vitest.dev/guide/snapshot.html

exports[`RiskGroup组件测试 > 正常渲染分组信息及风险项 1`] = `
<div class="container">
  <div class="groupHeader"><span>测试分组</span><span>5</span></div>
  <div class="groupContent">
    <div class="container" id="testId">
      <header class="header">
        <div class="collapseHeader active">
          <q-icon-stub type="icon-a-shixinxia1x"></q-icon-stub>
          <div class="scoreWrapper level2"><img src="/src/assets/images/icon-risk-level-red.svg" width="22" height="22" class="icon"><span class="score">80分</span></div><span class="text name">测试项</span><span class="text">3</span>
          <atooltip-stub trigger="hover" placement="right" transitionname="zoom-big" overlaystyle="[object Object]" overlayclassname="popoverContainer" prefixcls="ant-popover" mouseenterdelay="0.1" mouseleavedelay="0.1" autoadjustoverflow="true" align="[object Object]"><template>
              <div>
                <div class="ant-popover-inner-content">
                  <div>
                    <div class="container">
                      <div data-hit-type="should" class="group">
                        <div class="content">
                          <div data-testid="risk-hit-reason" class="record">
                            <header class="header">命中规则</header>
                            <div class="query"></div>
                          </div>
                        </div>
                      </div>
                    </div>
                  </div>
                </div>
              </div>
            </template>
            <q-icon-stub type="icon-a-shuomingxian"></q-icon-stub>
          </atooltip-stub>
        </div>
      </header>
      <div>
        <div>
          <div class="container">
            <div data-testid="el-loading" style="height: 160px;">
              <div class="fullsize" style="background-color: unset;"><span class="root" style="background-color: unset;"><div class="logo"><img src="/src/components/global/q-loading/images/part-1.svg" class="p1"><img src="/src/components/global/q-loading/images/part-2.svg" class="p2"><img src="/src/components/global/q-loading/images/part-3.svg" class="p3"></div><div class="name"><img src="/src/components/global/q-loading/images/qcc.svg" alt="企查查"></div></span></div>
            </div>
            <div class="container" tkey="1735718400000_testKey"></div>
          </div>
        </div>
      </div>
    </div>
  </div>
</div>
`;
