@import '@/styles/token.less';

// theme
@high-risk: @qcc-color-red-500;
@middle-risk: @qcc-color-yellow-500;
@low-risk: @qcc-color-green-500;

.themed(@color, @background-color) {
  .block > .title em {
    color: @color;
  }

  .dimension > .header em {
    color: @color;
  }
}

.themed-collapse(@color, @background-color) {
  .tag {
    color: @color;
    background-color: @background-color;
  }
}

.group {
  &:not(:first-child) {
    padding-top: 15px;
  }

  &:not(:last-child) {
    padding-bottom: 15px;
  }

  &.high {
    .themed(@high-risk, @qcc-color-red-300);
  }

  &.middle {
    .themed(@middle-risk, #fff4e0);
  }

  &.low {
    .themed(@low-risk, #e0f5ec);
  }

  > .block {
    > .title {
      line-height: 23px;
      font-size: 16px;
      padding-top: 20px;
      padding-bottom: 5px;
      display: flex;
      // align-items: flex-start;
      font-weight: 700;

      em {
        // color: @qcc-color-red-500;
        margin-left: 5px;
      }
    }
  }
}

.dimension {
  // &:not(:last-child) {
  //   margin-bottom: 15px;
  // }
  > .header {
    line-height: 23px;
    font-size: 16px;
    padding-top: 20px;
    padding-bottom: 5px;
    display: flex;
    align-items: flex-start;
    font-weight: 700;

    em {
      // color: @qcc-color-red-500;
      margin-left: 5px;
    }
  }
}

.collapse {
  &-wrapper {
    border-bottom: 1px solid #eee;

    & & {
      padding: 0 10px;

      &:last-child {
        border-bottom: none;
      }
    }
  }

  &-header {
    width: 100%;
    height: 58px;
    display: flex;
    align-items: center;
    gap: 8px;
    border-radius: 2px;
    padding: 0 4px;

    &:hover  {
      .arrow {
        color: #128BED;
      }
    }

    .arrow {
      color: #666;
    }

    .scoreWrapper {
      display: flex;
      align-items: center;
      justify-content: center;
      gap: 4px;
      padding: 2px 6px 2px 4px;
      border-radius: 2px;

      &.level0 {
        color: #00AD65;
        background-color: #E0F5EC;
      }

      &.level1 {
        color: #D97716;
        background-color: #FFF4E0;
      }

      &.level2 {
        color: #F04040;
        background-color: #FFECEC;
      }
    }

    .text {
      font-size: 16px;
      font-weight: 500;
      line-height: 24px;
      color: #333;
    }

    .hint {
      color: #d8d8d8;

      &:hover {
        color: #128bed;
      }
    }
  }

  &-body {
    padding-bottom: 15px;
  }
}


.group-header {
  height: 42px;
  display: flex;
  align-items: center;
  gap: 5px;
  font-size: 18px;
  font-weight: 500;
  line-height: 26px;
  color: #333;
  position: relative;
  z-index: 0;

  &::before {
    content: "";
    position: absolute;
    left: 0;
    top: 25px;
    width: 118px;
    height: 8px;
    background: linear-gradient(90deg, #88C5F5 0%, rgba(226, 241, 253, 0) 100%);
    z-index: -1;
  }
}

.group-content {
  display: flex;
  flex-direction: column;
}

.container {
  background-color: #fff;
  border-radius: 4px;
  padding: 0 16px;

  & + & {
    margin-top: 8px;
  }

}



.query {
  max-width: 600px;
}
