import { mount, shallowMount } from '@vue/test-utils';

import { useFetchRiskDimension } from '@/hooks/use-fetch-risk-dimension';

import DimensionContent from '..';
import { flushPromises } from '@/test-utils/flush-promises';

vi.mock('@/hooks/use-fetch-risk-dimension', () => ({
  useFetchRiskDimension: vi.fn(() => ({
    filterParams: {},
    initialAggs: {},
    pagination: { current: 1, pageSize: 10 },
    setPagination: vi.fn(),
    sort: {},
    setSort: vi.fn(),
    result: null,
    isLoading: false,
    isLoaded: false,
    search: vi.fn(),
  })),
}));

vi.mock('@/apps/investigation/pages/investigation-detail/widgets/dimension-content/config', () => ({
  filterConfigMap: {
    testKey: () => [{ key: 'test', doc_count: 1 }],
  },
}));

describe('DimensionContent 组件测试', () => {
  it('当 result 存在时，RiskTableNext 组件应正确接收数据源', async () => {
    const mockResult = { data: [], pagination: { current: 1, pageSize: 10 } };
    vi.mocked<any>(useFetchRiskDimension).mockImplementationOnce(() => ({
      filterParams: {},
      initialAggs: {},
      pagination: { current: 1, pageSize: 10 },
      setPagination: vi.fn(),
      sort: {},
      setSort: vi.fn(),
      result: mockResult,
      isLoading: false,
      isLoaded: true,
      search: vi.fn(),
    }));

    const wrapper = shallowMount(DimensionContent, {
      propsData: {
        meta: { key: 'testKey', snapshotId: '1', strategyId: '1' },
        dimensionStrategies: [{ dimensionStrategyId: '1' }],
        hitDetail: {
          hitDetails: {
            must: [],
            should: [],
            must_not: [],
          },
        },
      },
    });

    await flushPromises();
    const filterWrapper = wrapper.findComponent({ name: 'CommonSearchFilter' });
    expect(filterWrapper.exists()).toBe(false);
    expect(wrapper).toMatchSnapshot();
  });

  it('当 meta.key 不存在于 filterConfigMap 时，不应显示 CommonSearchFilter', async () => {
    const wrapper = mount(DimensionContent, {
      propsData: {
        meta: { key: 'nonExistentKey', snapshotId: '1', strategyId: '1' },
        dimensionStrategies: [{ dimensionStrategyId: '1' }],
        hitDetail: {
          hitDetails: {
            must: [],
            should: [],
            must_not: [],
          },
        },
      },
    });

    await wrapper.vm.$nextTick();
    const filterWrapper = wrapper.findComponent({ name: 'CommonSearchFilter' });
    expect(filterWrapper.exists()).toBe(false);
  });

  it('当 initialAggs 为空时，不应显示 CommonSearchFilter', async () => {
    const wrapper = mount(DimensionContent, {
      propsData: {
        meta: { key: 'testKey', snapshotId: '1', strategyId: '1' },
        dimensionStrategies: [{ dimensionStrategyId: '1' }],
        hitDetail: {
          hitDetails: {
            must: [],
            should: [],
            must_not: [],
          },
        },
      },
    });

    await wrapper.vm.$nextTick();
    const filterWrapper = wrapper.findComponent({ name: 'CommonSearchFilter' });
    expect(filterWrapper.exists()).toBe(false);
  });

  it('当 isLoading 为 true 且 result 为 null 时，应显示加载动画', async () => {
    vi.mocked<any>(useFetchRiskDimension).mockImplementationOnce(() => ({
      filterParams: {},
      initialAggs: {},
      pagination: { current: 1, pageSize: 10 },
      setPagination: vi.fn(),
      sort: {},
      setSort: vi.fn(),
      result: null,
      isLoading: true,
      isLoaded: false,
      search: vi.fn(),
    }));

    const wrapper = shallowMount(DimensionContent, {
      propsData: {
        meta: { key: 'testKey', snapshotId: '1', strategyId: '1' },
        dimensionStrategies: [{ dimensionStrategyId: '1' }],
        hitDetail: {
          hitDetails: {
            must: [],
            should: [],
            must_not: [],
          },
        },
      },
    });

    await wrapper.vm.$nextTick();
    const loadingWrapper = wrapper.find('[data-testid="el-loading"]');
    const styles = window.getComputedStyle(loadingWrapper.element);
    expect(styles.display).not.toBe('none');
  });

  it('当 needRefresh 为 true 时，应触发 search 方法', async () => {
    const searchMock = vi.fn();
    vi.mocked<any>(useFetchRiskDimension).mockImplementationOnce(() => ({
      filterParams: {},
      initialAggs: {},
      pagination: { current: 1, pageSize: 10 },
      setPagination: vi.fn(),
      sort: {},
      setSort: vi.fn(),
      result: null,
      isLoading: false,
      isLoaded: false,
      search: searchMock,
    }));

    const wrapper = shallowMount(DimensionContent, {
      propsData: {
        needRefresh: true,
        meta: { key: 'testKey', snapshotId: '1', strategyId: '1' },
        dimensionStrategies: [{ dimensionStrategyId: '1' }],
        hitDetail: {
          hitDetails: {
            must: [],
            should: [],
            must_not: [],
          },
        },
      },
    });

    await wrapper.vm.$nextTick();
    expect(searchMock).toHaveBeenCalled();
  });
});
