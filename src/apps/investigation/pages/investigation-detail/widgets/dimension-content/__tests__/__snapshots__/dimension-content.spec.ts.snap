// Vitest Snapshot v1, https://vitest.dev/guide/snapshot.html

exports[`DimensionContent 组件测试 > 当 result 存在时，RiskTableNext 组件应正确接收数据源 1`] = `
<div class="container">
  <div data-testid="el-loading" style="height: 160px; display: none;">
    <div class="fullsize" style="background-color: unset;"><span class="root" style="background-color: unset;"><div class="logo"><img src="/src/components/global/q-loading/images/part-1.svg" class="p1"><img src="/src/components/global/q-loading/images/part-2.svg" class="p2"><img src="/src/components/global/q-loading/images/part-3.svg" class="p3"></div><div class="name"><img src="/src/components/global/q-loading/images/qcc.svg" alt="企查查"></div></span></div>
  </div>
  <div class="container">
    <div class="tableWrapper">
      <div class="table">
        <div class="container">
          <div class="ant-spin-nested-loading">
            <div class="ant-spin-container">
              <div class="ant-table-wrapper table scrollable" emptytext="暂时没有找到相关数据" scrolltofirstrowonchange="true">
                <div class="ant-spin-nested-loading">
                  <div class="ant-spin-container">
                    <div class="ant-table ant-table-scroll-position-left ant-table-default ant-table-bordered ant-table-empty">
                      <div class="ant-table-content">
                        <!---->
                        <div class="ant-table-body">
                          <table class="">
                            <colgroup>
                              <col style="width: 58px; min-width: 58px;">
                            </colgroup>
                            <thead class="ant-table-thead">
                              <tr>
                                <th key="0" align="left" class="ant-table-align-left ant-table-row-cell-break-word ant-table-row-cell-last" style="text-align: left;"><span class="ant-table-header-column"><div><span class="ant-table-column-title">序号</span><span class="ant-table-column-sorter"></span>
                        </div></span></th>
                        </tr>
                        </thead>
                        <tbody class="ant-table-tbody"></tbody>
                        </table>
                      </div>
                      <div class="ant-table-placeholder">
                        <div class="container"><img src="/src/shared/components/assets/images/icon_empty_text.svg" width="50px" height="50px">
                          <div class="description">暂时没有找到相关数据</div>
                        </div>
                      </div>
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</div>
</div>
`;
