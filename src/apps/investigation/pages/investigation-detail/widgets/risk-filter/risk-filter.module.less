.container {
  padding: 8px 16px;
  display: flex;
  align-items: center;
  justify-content: space-between;
  background-color: #fff;
  border-radius: 4px;

  .item-list {
    display: flex;
    flex-wrap: wrap;
    gap: 8px;

    .item {
      display: inline-flex;
      font-size: 14px;
      line-height: 22px;
      padding: 3px 10px;
      border-radius: 2px;
      background: #f7f7f7;
      align-items: center;
      cursor: pointer;

      &:hover {
        background: #e2f1fd;
      }

      .icon {
        margin-right: 5px;
      }

      .icon0 {
        color: #13c261;
      }

      .icon1 {
        color: #fa0;
      }

      .icon2 {
        color: #F04040;
      }
    }

    .count {
      color: #999;
      margin-left: .5em;
    }

    .active {
      background: #e2f1fd;
      color: #128bed;
    }
  }
}
