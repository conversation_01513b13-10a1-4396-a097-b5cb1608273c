// Vitest Snapshot v1, https://vitest.dev/guide/snapshot.html

exports[`InvestigationHistoryPage > 正确渲染 1`] = `
<hero-layout>
  <div class="root">
    <div class="header">
      <div class="wrapper border">
        <div class="title">调查历史</div>
      </div>
    </div>
    <div class="body" style="padding-top: 0px;">
      <div class="container">
        <div class="main">
          <div class="root offset q-filter__root">
            <div class="group block q-filter-group--filters q-filter-group lastGroup filled">
              <div class="label q-filter-label" style="width: auto; padding-top: 0px;">筛选条件</div>
              <div class="wrap wrapGroups q-filter-wrap--filters">
                <q-select multiple="true" options="" renderreferencelabel="(value, option) => {
                if (option?.custom) {
                  return value;
                }
                if (!value) {
                  return '';
                }
                if (typeof value === 'number') {
                  return \` (\${String(value).trim()})\`;
                }
                return value;
              }" class="select"></q-select>
                <q-select multiple="true" options="" renderreferencelabel="(value, option) => {
                if (option?.custom) {
                  return value;
                }
                if (!value) {
                  return '';
                }
                if (typeof value === 'number') {
                  return \` (\${String(value).trim()})\`;
                }
                return value;
              }" class="select"></q-select>
                <q-select value="[object Object]" options="[object Object],[object Object],[object Object],[object Object],[object Object],[object Object],[object Object]" renderreferencelabel="(value, option) => {
                if (option?.custom) {
                  return value;
                }
                if (!value) {
                  return '';
                }
                if (typeof value === 'number') {
                  return \` (\${String(value).trim()})\`;
                }
                return value;
              }" custom="[object Object]" class="select"></q-select>
                <div style="display: inline-block;">
                  <div class="container tiny-search__container">
                    <div class="default tiny-search__default">
                      <div class="input">
                        <q-icon-stub type="icon-sousuo"></q-icon-stub><span>搜索</span>
                      </div>
                    </div>
                    <div class="search" style="width: 258px; display: none;"><span class="ant-input-search ant-input-search-enter-button ant-input-group-wrapper input tiny-search__input"><span class="ant-input-wrapper ant-input-group"><span class="ant-input-search ant-input-search-enter-button ant-input-affix-wrapper"><input placeholder="请输入企业名称" type="text" class="ant-input"><span class="ant-input-suffix"></span></span><span class="ant-input-group-addon"><button type="button" class="ant-btn ant-btn-primary tiny-search__search ant-input-search-button"><q-icon-stub type="icon-sousuo"></q-icon-stub></button></span></span></span>
                      <div class="clear" style="display: none;"><a class="tiny-search__cancel">取消</a></div>
                    </div>
                  </div>
                </div>
              </div>
              <div style="display: inline-block;">
                <div id="reset-filter-btn" class="aside"><button type="button" class="ant-btn ant-btn-link">
                    <q-icon-stub type="icon-chexiaozhongzhi"></q-icon-stub><span>重置筛选</span>
                  </button></div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
  <div class="root">
    <div class="header">
      <div class="wrapper border">
        <div class="title">
          <div>
            <div class="container"><span>共找到<i aria-label="icon: sync" class="anticon anticon-sync" style="display: none;"><svg viewBox="64 64 896 896" data-icon="sync" width="1em" height="1em" fill="currentColor" aria-hidden="true" focusable="false" class="anticon-spin"><path d="M168 504.2c1-43.7 10-86.1 26.9-126 17.3-41 42.1-77.7 73.7-109.4S337 212.3 378 195c42.4-17.9 87.4-27 133.9-27s91.5 9.1 133.8 27A341.5 341.5 0 0 1 755 268.8c9.9 9.9 19.2 20.4 27.8 31.4l-60.2 47a8 8 0 0 0 3 14.1l175.7 43c5 1.2 9.9-2.6 9.9-7.7l.8-180.9c0-6.7-7.7-10.5-12.9-6.3l-56.4 44.1C765.8 155.1 646.2 92 511.8 92 282.7 92 96.3 275.6 92 503.8a8 8 0 0 0 8 8.2h60c4.4 0 7.9-3.5 8-7.8zm756 7.8h-60c-4.4 0-7.9 3.5-8 7.8-1 43.7-10 86.1-26.9 126-17.3 41-42.1 77.8-73.7 109.4A342.45 342.45 0 0 1 512.1 856a342.24 342.24 0 0 1-243.2-100.8c-9.9-9.9-19.2-20.4-27.8-31.4l60.2-47a8 8 0 0 0-3-14.1l-175.7-43c-5-1.2-9.9 2.6-9.9 7.7l-.7 181c0 6.7 7.7 10.5 12.9 6.3l56.4-44.1C258.2 868.9 377.8 932 512.2 932c229.2 0 415.5-183.7 419.8-411.8a8 8 0 0 0-8-8.2z"></path></svg></i><em>3</em>条相关结果</span></div>
          </div>
        </div>
      </div>
    </div>
    <div class="body" style="padding: 15px;">
      <div class="container">
        <div class="ant-spin-nested-loading">
          <div class="ant-spin-container">
            <div class="ant-table-wrapper table scrollable" emptytext="暂时没有找到相关数据" scrolltofirstrowonchange="true">
              <div class="ant-spin-nested-loading">
                <div class="ant-spin-container">
                  <div class="ant-table ant-table-scroll-position-left ant-table-default ant-table-bordered">
                    <div class="ant-table-content">
                      <!---->
                      <div class="ant-table-body">
                        <table class="">
                          <colgroup>
                            <col class="ant-table-selection-col">
                            <col style="width: 358px; min-width: 358px;">
                            <col style="width: 173px; min-width: 173px;">
                            <col style="width: 104px; min-width: 104px;">
                            <col style="width: 104px; min-width: 104px;">
                            <col style="width: 165px; min-width: 165px;">
                            <col style="width: 100px; min-width: 100px;">
                          </colgroup>
                          <thead class="ant-table-thead">
                            <tr>
                              <th key="selection-column" align="left" class="ant-table-selection-column"><span class="ant-table-header-column"><div><span class="ant-table-column-title"><div class="ant-table-selection"><label class="ant-checkbox-wrapper"><span class="ant-checkbox"><input type="checkbox" class="ant-checkbox-input" value=""><span class="ant-checkbox-inner"></span></span></label>
                      </div></span><span class="ant-table-column-sorter"></span>
                    </div></span></th>
                    <th key="1" align="left" class="ant-table-row-cell-break-word"><span class="ant-table-header-column"><div><span class="ant-table-column-title">企业名称</span><span class="ant-table-column-sorter"></span>
                  </div></span></th>
                  <th key="orgModel.modelName" align="left" class="ant-table-row-cell-break-word"><span class="ant-table-header-column"><div><span class="ant-table-column-title">尽调模型</span><span class="ant-table-column-sorter"></span>
                </div></span></th>
                <th key="result" align="left" class="ant-table-column-has-actions ant-table-column-has-sorters ant-table-align-left ant-table-row-cell-break-word" style="text-align: left;"><span class="ant-table-header-column"><div class="ant-table-column-sorters"><span class="ant-table-column-title">风险等级</span><span class="ant-table-column-sorter"><div title="Sort" class="ant-table-column-sorter-inner ant-table-column-sorter-inner-full"><i aria-label="icon: caret-up" class="anticon anticon-caret-up ant-table-column-sorter-up off"><svg viewBox="0 0 1024 1024" data-icon="caret-up" width="1em" height="1em" fill="currentColor" aria-hidden="true" focusable="false" class=""><path d="M858.9 689L530.5 308.2c-9.4-10.9-27.5-10.9-37 0L165.1 689c-12.2 14.2-1.2 35 18.5 35h656.8c19.7 0 30.7-20.8 18.5-35z"></path></svg></i><i aria-label="icon: caret-down" class="anticon anticon-caret-down ant-table-column-sorter-down off"><svg viewBox="0 0 1024 1024" data-icon="caret-down" width="1em" height="1em" fill="currentColor" aria-hidden="true" focusable="false" class=""><path d="M840.4 300H183.6c-19.7 0-30.7 20.8-18.5 35l328.4 380.8c9.4 10.9 27.5 10.9 37 0L858.9 335c12.2-14.2 1.2-35-18.5-35z"></path></svg></i></div></span>
              </div></span></th>
              <th key="operator" align="left" class="ant-table-row-cell-break-word"><span class="ant-table-header-column"><div><span class="ant-table-column-title">操作人</span><span class="ant-table-column-sorter"></span>
            </div></span></th>
            <th key="createDate" align="left" class="ant-table-column-has-actions ant-table-column-has-sorters ant-table-row-cell-break-word"><span class="ant-table-header-column"><div class="ant-table-column-sorters"><span class="ant-table-column-title">排查时间</span><span class="ant-table-column-sorter"><div title="Sort" class="ant-table-column-sorter-inner ant-table-column-sorter-inner-full"><i aria-label="icon: caret-up" class="anticon anticon-caret-up ant-table-column-sorter-up off"><svg viewBox="0 0 1024 1024" data-icon="caret-up" width="1em" height="1em" fill="currentColor" aria-hidden="true" focusable="false" class=""><path d="M858.9 689L530.5 308.2c-9.4-10.9-27.5-10.9-37 0L165.1 689c-12.2 14.2-1.2 35 18.5 35h656.8c19.7 0 30.7-20.8 18.5-35z"></path></svg></i><i aria-label="icon: caret-down" class="anticon anticon-caret-down ant-table-column-sorter-down off"><svg viewBox="0 0 1024 1024" data-icon="caret-down" width="1em" height="1em" fill="currentColor" aria-hidden="true" focusable="false" class=""><path d="M840.4 300H183.6c-19.7 0-30.7 20.8-18.5 35l328.4 380.8c9.4 10.9 27.5 10.9 37 0L858.9 335c12.2-14.2 1.2-35-18.5-35z"></path></svg></i></div></span>
          </div></span></th>
          <th key="6" align="left" class="ant-table-row-cell-break-word ant-table-row-cell-last"><span class="ant-table-header-column"><div><span class="ant-table-column-title">操作</span><span class="ant-table-column-sorter"></span>
        </div></span></th>
        </tr>
        </thead>
        <tbody class="ant-table-tbody">
          <tr class="ant-table-row ant-table-row-level-0" data-row-key="50996905">
            <td class="ant-table-selection-column"><span><label class="ant-checkbox-wrapper"><span class="ant-checkbox"><input type="checkbox" class="ant-checkbox-input" value=""><span class="ant-checkbox-inner"></span></span></label></span></td>
            <td class="ant-table-row-cell-break-word"><a href="/embed/companyDetail?keyNo=84c17a005a759a5e0d875c1ebb6c9846&amp;title=乐视网信息技术（北京）股份有限公司" target="_blank" class="companyLink">乐视网信息技术（北京）股份有限公司</a></td>
            <td class="ant-table-row-cell-break-word">中行山东分行模型031801</td>
            <td class="ant-table-column-has-actions ant-table-column-has-sorters ant-table-row-cell-break-word" style="text-align: left;"><span class="" style="color: #666; background: #FFDECC; display: inline-block; padding: 1px 4px; height: 20px; line-height: 18px; border-radius: 2px; cursor: pointer;">中高风险</span></td>
            <td class="ant-table-row-cell-break-word">-</td>
            <td class="ant-table-column-has-actions ant-table-column-has-sorters ant-table-row-cell-break-word">2025-03-25 10:46:16</td>
            <td class="ant-table-row-cell-break-word"><button type="button" class="ant-btn ant-btn-link"><span>排查详情</span></button></td>
          </tr>
          <tr class="ant-table-row ant-table-row-level-0" data-row-key="50996903">
            <td class="ant-table-selection-column"><span><label class="ant-checkbox-wrapper"><span class="ant-checkbox"><input type="checkbox" class="ant-checkbox-input" value=""><span class="ant-checkbox-inner"></span></span></label></span></td>
            <td class="ant-table-row-cell-break-word"><a href="/embed/companyDetail?keyNo=84c17a005a759a5e0d875c1ebb6c9846&amp;title=乐视网信息技术（北京）股份有限公司" target="_blank" class="companyLink">乐视网信息技术（北京）股份有限公司</a></td>
            <td class="ant-table-row-cell-break-word">中行山东分行模型031901</td>
            <td class="ant-table-column-has-actions ant-table-column-has-sorters ant-table-row-cell-break-word" style="text-align: left;"><span class="" style="color: #666; background: #FFDECC; display: inline-block; padding: 1px 4px; height: 20px; line-height: 18px; border-radius: 2px; cursor: pointer;">中高风险</span></td>
            <td class="ant-table-row-cell-break-word">张三</td>
            <td class="ant-table-column-has-actions ant-table-column-has-sorters ant-table-row-cell-break-word">2025-03-25 10:42:26</td>
            <td class="ant-table-row-cell-break-word"><button type="button" class="ant-btn ant-btn-link"><span>排查详情</span></button></td>
          </tr>
          <tr class="ant-table-row ant-table-row-level-0" data-row-key="50996904">
            <td class="ant-table-selection-column"><span><label class="ant-checkbox-wrapper"><span class="ant-checkbox"><input type="checkbox" class="ant-checkbox-input" value=""><span class="ant-checkbox-inner"></span></span></label></span></td>
            <td class="ant-table-row-cell-break-word"><a href="/embed/companyDetail?keyNo=84c17a005a759a5e0d875c1ebb6c9846&amp;title=乐视网信息技术（北京）股份有限公司" target="_blank" class="companyLink">乐视网信息技术（北京）股份有限公司</a></td>
            <td class="ant-table-row-cell-break-word">中行山东分行模型031902</td>
            <td class="ant-table-column-has-actions ant-table-column-has-sorters ant-table-row-cell-break-word" style="text-align: left;"><span class="" style="color: #666; background: #FFCCCC; display: inline-block; padding: 1px 4px; height: 20px; line-height: 18px; border-radius: 2px; cursor: pointer;">高风险</span></td>
            <td class="ant-table-row-cell-break-word">张三</td>
            <td class="ant-table-column-has-actions ant-table-column-has-sorters ant-table-row-cell-break-word">2025-03-25 10:42:26</td>
            <td class="ant-table-row-cell-break-word"><button type="button" class="ant-btn ant-btn-link"><span>排查详情</span></button></td>
          </tr>
        </tbody>
        </table>
      </div>
    </div>
  </div>
  </div>
  </div>
  </div>
  </div>
  </div>
  </div>
  </div>
  </div>
</hero-layout>
`;
