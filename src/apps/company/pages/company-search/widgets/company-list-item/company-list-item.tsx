import _ from 'lodash';
import moment from 'moment';
import { message, Tooltip } from 'ant-design-vue';
import { defineComponent, PropType } from 'vue';

import QEntityAvatar from '@/components/global/q-entity-avatar';
import { ICompanyQcc } from '@/interfaces';
import { getTagsV2 } from '@/utils/company/company-tags';
import { isValidCompanyType, validateCompanyWithCountInfo } from '@/utils/company/company-type';
import QCopy from '@/components/global/q-copy';
import QTag from '@/components/global/q-tag';
import QPhoneStatus from '@/components/global/q-phone-status';
import QLink from '@/components/global/q-link';
import QIcon from '@/components/global/q-icon';
import CompanyStatus from '@/components/global/q-company-status';
import { createTrackEvent } from '@/config/tracking-events';
import { translateRegistCapiLabel } from '@/utils';

import CheckSvg from './images/check.svg';
import styles from './company-list-item.module.less';

export const kvOperMap = {
  1: '法定代表人',
  2: '执行事务合伙人',
  3: '负责人',
  4: '经营者',
  5: '投资人',
  6: '董事长',
  7: '理事长',
  8: '代表人',
};
const excludeArr = ['公司名称', '公司地址', '法定代表人'];

const CompanyListItem = defineComponent({
  name: 'CompanyListItem',
  props: {
    dataSource: {
      type: Object as PropType<ICompanyQcc>,
      required: true,
    },
  },
  methods: {
    onShowContactMore(type, list, id?) {
      const that = this as any;
      if (type === 'email') {
        that.$modal.showDimension('companyContact', { title: '更多邮箱', size: 'medium' }, { type, list });
        this.$track(createTrackEvent(7717, '搜索结果', '更多邮箱'));
      } else if (type === 'tel') {
        this.$service.company
          .getDetail({
            keyNo: id,
          })
          .then((res) => {
            that.$modal.showDimension(
              'companyContact',
              {
                size: 'medium',
              },
              {
                type,
                list,
                info: res,
              }
            );
            this.$track(createTrackEvent(7717, '搜索结果', '更多电话'));
          });
      }
    },
    createEmailNode(email: string) {
      return <a href={`mailto:${email}`} domPropsInnerHTML={email}></a>;
    },
    genEmailField(emailList, email): JSX.Element {
      if (!emailList.length) {
        if (!email) {
          return <span>-</span>;
        }
        return this.createEmailNode(email);
      }

      const singleNode = this.createEmailNode(email);
      if (emailList && emailList.length > 0) {
        return (
          <span>
            {singleNode}{' '}
            <a
              onClick={(e) => {
                e.stopPropagation();
                (this as any).onShowContactMore('email', emailList);
              }}
            >
              更多 {emailList.length}
            </a>
          </span>
        );
      }
      return singleNode;
    },
    genTelField(telList: string, tel: string, id, commonlist) {
      let list: Record<string, any>;
      try {
        list = JSON.parse(telList || '[]');
      } catch (err) {
        list = [];
      }
      if (!list.length || !tel) {
        return <span>{tel || '-'}</span>;
      }
      const telInfo = commonlist.filter((item) => item.k === '5')[0];
      if (list && list.length) {
        return (
          <a
            onClick={(e) => {
              e.stopPropagation();
              (this as any).onShowContactMore('tel', list, id);
            }}
          >
            <QPhoneStatus phone={tel} vtList={JSON.parse(telInfo?.v || '[]')} />
            <span class={[styles.phone, 'text-#666']} domPropsInnerHTML={tel}></span> 更多 {list.length}
          </a>
        );
      }
      return (
        <a>
          <QPhoneStatus phone={tel} vtList={JSON.parse(telInfo?.v || '[]')} />
          <span class={[styles.phone, 'text-#666']} domPropsInnerHTML={tel}></span>
        </a>
      );
    },
    genWebsiteField(website: string, dataSource) {
      return (
        <span class={styles.gw}>
          {website ? (
            <a
              href={website.indexOf('http') === 0 ? website : `http://${website}`}
              onClick={(e) => {
                e.stopPropagation();
                this.$track(createTrackEvent(7717, '搜索结果', '官网'));
              }}
              target="_blank"
              rel="noopener noreferrer"
            >
              {dataSource.Flag?.includes('WEB_ISICP') ? (
                <Tooltip>
                  <div slot="title">
                    <p>有ICP备案</p>
                    {/* {dataSource.InsuredYear ? <p>{dataSource.InsuredYear}年报公示</p> : null} */}
                  </div>
                  <img src={CheckSvg} width="18" alt="website" />
                </Tooltip>
              ) : null}
              <span>{website}</span>
            </a>
          ) : (
            '-'
          )}
        </span>
      );
    },
    async linkDetail() {
      if ((this.dataSource.IsHide || validateCompanyWithCountInfo(this.dataSource)) && this.$route.query.from !== 'searchbar') {
        message.warning('注册地在中国香港、中国澳门、中国台湾以及境外的企业、机关单位等暂不支持风险排查');
        return;
      }

      this.$emit('click', this.dataSource);
    },
    renderHitReasons(row: ICompanyQcc) {
      const { HitReasons } = row;
      const nodeList: JSX.Element[] = [];

      // 完全匹配的样式会不一样，所以需要判断
      const isFullMatch = (str: string) => {
        return str.startsWith('<em>') && str.endsWith('</em>');
      };
      HitReasons.forEach(({ Field, Value }) => {
        if (!excludeArr.includes(Field)) {
          nodeList.push(
            <span class={styles.hit}>
              <span class={styles.field}>{Field} </span>:{' '}
              <span class={{ [styles.fullMatch]: isFullMatch(Value) }} domPropsInnerHTML={Value.split('。')} />
            </span>
          );
        }
      });
      return nodeList.slice(0, 3);
    },
    renderOperType() {
      const { KeyNo, OperInfo } = this.dataSource;

      const firstC = KeyNo[0];
      const operInfo: Record<string, string | number | boolean> = JSON.parse(OperInfo || '{}');
      let label = '';

      if (_.isObject(operInfo)) {
        label = kvOperMap[operInfo.t as string | number] || '法定代表人';
      } else {
        switch (firstC) {
          case 'l':
          case 'h':
            label = '董事长';
            break;
          case 'w':
            label = '负责人';
            break;
          case 'j':
            label = '理事长';
            break;
          default:
            label = '法定代表人';
            break;
        }
      }

      return label;
    },
    renderOperInfo() {
      const { OperInfo, OperName, CountInfo } = this.dataSource;
      const operInfo = JSON.parse(OperInfo || '{}');
      if (CountInfo) {
        const operList = CountInfo?.filter((item) => item.k === '10');
        if (operList?.length) {
          const operData = JSON.parse(_.get(operList, '[0].v', '{}'));
          return this.renderOper(operData.OperList, OperName);
        }
      }
      return this.renderOper([operInfo], OperName);
    },
    renderOper(operList, name): any {
      const opername = name;
      const noTagsStr = opername?.replace(/<[^>]+>/g, '');

      return operList.map((item, index) => {
        const node: any = [];
        if (index > 0) {
          node.push('、');
        }

        // 公司0 香港公司3 台湾公司5
        if ([0, 3, 5].includes(item?.o)) {
          node.push(
            <QLink
              onClick={(e) => {
                e.stopPropagation();
                this.$router.push(`/supplier/investigation-history/${item.k}?title=${item.n}`);
              }}
              domPropsInnerHTML={item.n === noTagsStr ? opername || name : item.n || name}
            />
          );
          return node;
        }
        if (item.k) {
          node.push(
            <QLink
              onClick={(e) => {
                e.stopPropagation();
                window.open(`/embed/beneficaryDetail?personId=${item.k}&title=${item.n}`);
              }}
              domPropsInnerHTML={item.n === noTagsStr ? opername || name : item.n || name}
            />
          );
          return node;
        }
        node.push(
          item.n?.length ? (
            <QLink
              onClick={(e) => {
                e.stopPropagation();
                console.log(item, 1);

                window.open(`/embed/beneficaryDetail?personId=${item.k}_${item?.n}&title=${item.n}`);
              }}
              domPropsInnerHTML={item.n === noTagsStr ? opername || name : item.n || name}
            />
          ) : (
            '-'
          )
        );
        return node;
      });
    },
    getHTMLText(html) {
      const div = document.createElement('div');
      div.innerHTML = html;
      return div.textContent || div.innerText;
    },
  },
  render() {
    const { dataSource } = this;

    const companyTags = getTagsV2(dataSource.TagsInfoV2 ?? []);
    const logo = dataSource.ImageUrl;
    const hitReasonName = (dataSource.HitReasons || []).filter((item) => item.Field === '公司名称');
    const companyName = dataSource.Name || _.get(hitReasonName, '[0].Value');
    const omitNameArr = (dataSource.HitReasons || []).filter((item) => !excludeArr.includes(item.Field) && item.Value);
    const hasContact = dataSource.ContactNumber || dataSource.Email || dataSource.GW;
    const isUnsupportedCompany =
      (!isValidCompanyType(dataSource.Type) || validateCompanyWithCountInfo(dataSource)) && this.$route.query.from !== 'searchbar';
    return (
      <div
        class={{
          [styles.root]: true,
          [styles.isHide]: isUnsupportedCompany,
        }}
        onClick={this.linkDetail}
      >
        <div class={styles.img}>
          <QEntityAvatar size={80} src={logo} radius={4} />
        </div>
        <div class={styles.main}>
          <div class={styles.bolded}>
            <QCopy
              copyValue={this.getHTMLText(companyName)}
              style={{ display: 'flex', alignItems: 'center', maxWidth: 'calc(100% - 60px)' }}
            >
              <div slot="contain" class={styles.copyValue}>
                <span class={[styles.name, 'copyContent']} domPropsInnerHTML={companyName}></span>
                {dataSource.ShortStatus ? <CompanyStatus status={dataSource.ShortStatus} ghost /> : null}
              </div>
            </QCopy>
          </div>

          <div class={styles.labels} v-show={!!companyTags.length}>
            {companyTags.map(({ label, color }) => (
              <QTag customClass={styles.tas} type={color} key={label}>
                <span domPropsInnerHTML={label.replace(/\|/g, '<span>|</span>')}></span>
              </QTag>
            ))}
          </div>
          {dataSource?.IsHide ? (
            <div style={{ marginTop: '10px' }}>
              <div class={styles.illegalText}>根据全国社会组织信用信息公示平台显示，该组织为涉嫌非法社会组织</div>
            </div>
          ) : (
            <div>
              <ul class={styles.line}>
                <li class={'flex'}>
                  <span style={{ flex: 1 }}>{this.renderOperType()}：</span>
                  <div class={'flex flex-wrap'}>{this.renderOperInfo()}</div>
                </li>
                <li>
                  <span class="text-#999">
                    {translateRegistCapiLabel(
                      dataSource.KeyNo?.startsWith('s') ? 'org' : dataSource.CountInfo.find((item) => item.k === '30')?.v?.split(',')
                    )}
                    ：
                  </span>
                  <span class="text-#666">{dataSource.RegistCapi || '-'}</span>
                </li>
                <li>
                  <span>成立日期：</span>
                  {dataSource.StartDate ? moment(dataSource.StartDate).format('YYYY-MM-DD') : '-'}
                </li>
                <li>
                  <span class="text-#999">统一社会信用代码：</span>
                  <QCopy copyValue={this.getHTMLText(dataSource.CreditCode) || '-'} style={{ display: 'inline-flex' }}>
                    <span slot="contain" class={styles.copyValue}>
                      <span class="copyContent text-#666" domPropsInnerHTML={dataSource.CreditCode || '-'}></span>
                    </span>
                  </QCopy>
                </li>
              </ul>
              {hasContact ? (
                <ul class={styles.line}>
                  <li>
                    <span class="text-#999">电话：</span>
                    <span>{this.genTelField(dataSource.TelList, dataSource.ContactNumber, dataSource.KeyNo, dataSource.CountInfo)}</span>
                  </li>
                  <li>
                    <span>邮箱：</span>
                    {this.genEmailField(dataSource.EmailList, dataSource.Email)}
                  </li>
                  <li>
                    <span>官网：</span>
                    {this.genWebsiteField(dataSource.GW, dataSource)}
                  </li>
                </ul>
              ) : null}
              <ul class={styles.line}>
                <li>
                  <span>地址：</span>
                  <QCopy copyValue={this.getHTMLText(dataSource.Address) || '-'} style={{ display: 'inline-flex' }}>
                    <span slot="contain" class={styles.copyValue}>
                      <span class="copyContent text-#666" domPropsInnerHTML={dataSource.Address || '-'}></span>
                    </span>
                  </QCopy>
                </li>
              </ul>
              {omitNameArr?.length ? (
                <ul class={styles.hits}>
                  <QIcon type="icon-guanlianwenjian" />
                  {this.renderHitReasons(dataSource as ICompanyQcc)}
                </ul>
              ) : null}
            </div>
          )}
        </div>

        {typeof this.$scopedSlots.extra === 'function' && !isUnsupportedCompany ? this.$scopedSlots.extra(dataSource) : null}

        <div style={{ height: '15px', width: '100%' }}></div>

        {isUnsupportedCompany ? (
          <div class={['flex items-center', styles.alert]}>
            <span class="font-bold" style={{ color: '#FFAA00' }}>
              该企业不支持风险排查
            </span>
            <span style={{ color: '#d8d8d8' }}>|</span>
            <span style="flex: 1;">注册地在中国香港、中国澳门、中国台湾以及境外的企业、机关单位等暂不支持风险排查</span>
          </div>
        ) : null}
      </div>
    );
  },
});

export default CompanyListItem;
