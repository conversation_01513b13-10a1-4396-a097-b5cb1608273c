import { riskLevelMap, DimensionFieldCompareTypeEnums, OperatorName, OperatorIcon, getCycleDes, getSelfOptions, ModelStatusMap } from '..';

describe('Risk Model Config', () => {
  test('riskLevelMap should have correct values', () => {
    expect(riskLevelMap).toEqual([
      { value: 2, icon: 'icon-icon_hongsedengji', label: '红色等级' },
      { value: 1, icon: 'icon-icon_huangsedengji', label: '黄色等级' },
      { value: 0, icon: 'icon-icon_lvsedengji', label: '绿色等级' },
    ]);
  });

  test('OperatorName should return correct names for each compare type', () => {
    expect(OperatorName[DimensionFieldCompareTypeEnums.GreaterThan]).toBe('大于');
    expect(OperatorName[DimensionFieldCompareTypeEnums.GreaterThanOrEqual]).toBe('大于等于');
    expect(OperatorName[DimensionFieldCompareTypeEnums.Equal]).toBe('等于');
    expect(OperatorName[DimensionFieldCompareTypeEnums.LessThan]).toBe('小于等于');
    expect(OperatorName[DimensionFieldCompareTypeEnums.LessThanOrEqual]).toBe('小于');
    expect(OperatorName[DimensionFieldCompareTypeEnums.ContainsAny]).toBe('包含任一');
    expect(OperatorName[DimensionFieldCompareTypeEnums.ContainsAll]).toBe('包含全部');
    expect(OperatorName[DimensionFieldCompareTypeEnums.ExceptAny]).toBe('任一不包含');
    expect(OperatorName[DimensionFieldCompareTypeEnums.ExceptAll]).toBe('都不包含');
  });

  test('OperatorIcon should return correct icons for each compare type', () => {
    expect(OperatorIcon[DimensionFieldCompareTypeEnums.GreaterThan]).toBe('>');
    expect(OperatorIcon[DimensionFieldCompareTypeEnums.GreaterThanOrEqual]).toBe('>=');
    expect(OperatorIcon[DimensionFieldCompareTypeEnums.LessThan]).toBe('<');
    expect(OperatorIcon[DimensionFieldCompareTypeEnums.LessThanOrEqual]).toBe('<=');
    expect(OperatorIcon[DimensionFieldCompareTypeEnums.ExceptAll]).toBe('都不包含');
    expect(OperatorIcon[DimensionFieldCompareTypeEnums.ExceptAny]).toBe('不包含');
  });

  test('getCycleDes should return "不限" when value is -1', () => {
    expect(getCycleDes('GreaterThan', -1)).toBe('不限');
    expect(getCycleDes('LessThanOrEqual', -1)).toBe('不限');
  });

  test('getCycleDes should return correct description for "GreaterThan" type', () => {
    expect(getCycleDes('GreaterThan', 5)).toBe('近5年');
  });

  test('getCycleDes should return correct description for "LessThanOrEqual" type', () => {
    expect(getCycleDes('LessThanOrEqual', 10)).toBe('10年前');
  });

  test('getCycleDes should return empty string for unknown type', () => {
    expect(getCycleDes('UnknownType', 5)).toBe('');
  });

  test('getSelfOptions should return options with correct labels for "GreaterThan" compare type', () => {
    const options = [1, 2, 3, -1];
    const result = getSelfOptions(2, 'GreaterThan', 'cycle', options);
    expect(result).toEqual([
      { value: 1, label: '近1年' },
      { value: 2, label: '近2年' },
      { value: 3, label: '近3年' },
      { value: -1, label: '不限' },
    ]);
  });

  test('getSelfOptions should return options with correct labels for "LessThanOrEqual" compare type', () => {
    const options = [1, 2, 3, -1];
    const result = getSelfOptions(2, 'LessThanOrEqual', 'cycle', options);
    expect(result).toEqual([
      { value: 1, label: '1年前' },
      { value: 2, label: '2年前' },
      { value: 3, label: '3年前' },
      { value: -1, label: '不限' },
    ]);
  });

  test('getSelfOptions should return original options when dimensionField is not "cycle"', () => {
    const options = ['a', 'b', 'c'];
    const result = getSelfOptions(2, 'GreaterThan', 'textField', options);
    expect(result).toEqual(options);
  });

  test('getSelfOptions should return original options when compareType is not "GreaterThan" or "LessThanOrEqual"', () => {
    const options = [1, 2, 3];
    const result = getSelfOptions(2, 'Between', 'cycle', options);
    expect(result).toEqual(options);
  });

  test('ModelStatusMap should have correct values for each status', () => {
    expect(ModelStatusMap[0]).toEqual({ label: '无效', style: { color: '#F04040', backgroundColor: '#ffecec' } });
    expect(ModelStatusMap[1]).toEqual({ label: '启用', style: { color: '#00ad65', backgroundColor: '#E0F5EC' } });
    expect(ModelStatusMap[2]).toEqual({ label: '开发中', style: { color: '#F04040', backgroundColor: '#ffecec' } });
    expect(ModelStatusMap[3]).toEqual({ label: '待废弃', style: { color: '#F04040', backgroundColor: '#ffecec' } });
    expect(ModelStatusMap[4]).toEqual({ label: '已废弃', style: { color: '#F04040', backgroundColor: '#ffecec' } });
  });
});
