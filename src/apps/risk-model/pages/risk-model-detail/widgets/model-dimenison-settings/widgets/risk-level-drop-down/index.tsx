import { computed, defineComponent, inject, ref, unref } from 'vue';
import { Dropdown, Menu } from 'ant-design-vue';

import { monitorRiskLevelMap, riskLevelMap } from '@/apps/risk-model/config';

import styles from './risk-level-drop-down.module.less';

// 风险等级选择组件
const RiskLevelDropDown = defineComponent({
  props: {
    value: {
      type: [String, Number],
      default: '0',
    },
    disabled: {
      type: Boolean,
      default: false, // 是否禁用
    },
  },
  model: {
    prop: 'value',
    event: 'change',
  },
  setup() {
    const visible = ref(false);
    const isMonitor = inject('isMonitor', false);
    const riskMap = computed(() => {
      return unref(isMonitor) ? monitorRiskLevelMap : riskLevelMap;
    });

    return {
      visible,
      riskMap,
    };
  },
  render() {
    return (
      <Dropdown v-model={this.visible} overlayClassName={styles.overlay} getPopupContainer={(node) => node.parentNode} trigger={['click']}>
        <div class={styles.downArrow} data-test="risk-level-drop-down">
          <q-icon style={{ fontSize: '22px' }} type={this.riskMap.find((item) => item.value === +this.value)?.icon} />
          {!this.disabled && (
            <q-icon
              data-testid="icon-down-arrow"
              type={this.visible ? 'icon-a-shixinshang1x' : 'icon-a-shixinxia1x'}
              style={{ color: this.visible ? ' #1890ff ' : '#666' }}
            />
          )}
        </div>
        {!this.disabled && (
          <Menu
            slot="overlay"
            onClick={(e) => {
              this.visible = false;
              this.$emit('change', e.key);
            }}
          >
            {this.riskMap.map((item) => (
              <Menu.Item key={item.value} class="flex items-center" data-testid="risk-level-item">
                <q-icon style={{ fontSize: '22px', marginRight: '4px' }} type={item.icon}></q-icon> {item.label}
              </Menu.Item>
            ))}
            <Menu.Item disabled style="cursor: default">
              <div>「红色等级」被命中</div>
              <div>
                结果直接变为<span style={{ color: '#a80000' }}>「高风险」</span>
              </div>
            </Menu.Item>
          </Menu>
        )}
      </Dropdown>
    );
  },
});

export default RiskLevelDropDown;
