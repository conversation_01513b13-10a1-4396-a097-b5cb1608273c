import { computed, defineComponent, onMounted, onUnmounted, ref, unref } from 'vue';
import { Button, message } from 'ant-design-vue';
import { differenceBy, escape, uniqBy } from 'lodash';

import QModal from '@/components/global/q-modal/q-modal';
import { createPromiseDialog } from '@/components/promise-dialogs';
import { monitor } from '@/shared/services';
import QRichTable from '@/components/global/q-rich-table';
import WarningInfo from '@/shared/components/warning-info';
import { Permission } from '@/config/permissions.config';
import { hasPermission } from '@/shared/composables/use-permission';

import styles from '../../company-related-pop.module.less';
import { useTrendCompanies } from '../../../../hooks/use-trend-companies';
import SearchInput from '../../../search-input';

const MAXCOUNT = 6000;

const TABLE_COLUMNS = [
  {
    title: '序号',
    width: 50,
    customRender: (text, row, index) => index + 1,
  },
  {
    title: '企业名称',
    scopedSlots: { customRender: 'company' },
  },

  {
    title: '状态',
    width: 120,
    scopedSlots: { customRender: 'status' },
  },
];

const RelatedDetailModal = defineComponent({
  name: 'RelatedDetailModal',
  props: {
    params: {
      type: Object,
      default: () => ({}),
    },
  },
  setup(props, { emit }) {
    const visible = ref(false);

    const availableData = ref([]);

    const { totalData, pagination, removeScrollListener, initScrollListener, resetList, isLoading, prePayload } = useTrendCompanies(
      props.params.record,
      undefined,
      undefined
    );

    const selection = ref<any[]>([]);

    const rowSelection = computed(() => ({
      selectedRowKeys: selection.value.map((item: any) => item.companyKeynoRelated),
      checkStrictly: false,
      onChange: (selectedRowKeys, selectRow) => {
        selection.value = uniqBy(
          [...differenceBy(unref(selection), unref(totalData), 'companyKeynoRelated'), ...selectRow],
          'companyKeynoRelated'
        );
      },
      getCheckboxProps: (record) => ({
        props: {
          disabled: !!record.operate || record.isMonitor,
        },
      }),
    }));

    // 检测数据是否变化，变化要去刷新页面
    const staticChanged = ref(false);

    // 未监控的公司数量
    const unMonitorCount = ref(0);

    const getUnMonitorCount = async () => {
      try {
        const res = await monitor.getRelatedUnMonitorCompanyRelatedCount(prePayload.value);
        unMonitorCount.value = res.unMonitorCompanyCount;
      } catch (error) {
        console.log(error);
      }
    };

    const isSubmitting = ref(false);

    const close = () => {
      visible.value = false;
      emit('resolve', staticChanged.value);
    };

    const handleSearch = (keyword: string) => {
      if (prePayload.value.esFilter.filter.companyName === keyword) return;
      prePayload.value.esFilter.filter.companyName = keyword;
      resetList();
    };
    /**
     * 添加监控
     */
    const addCompany = async (dataset) => {
      isSubmitting.value = true;
      try {
        await monitor.addRelatedCompany({
          items: dataset,
          companyId: props.params.record.companyId,
          monitorGroupId: props.params.record.monitorGroupId,
        });
        isSubmitting.value = false;
        staticChanged.value = true;
        close();
      } catch (error) {
        isSubmitting.value = false;
      }
    };

    /**
     * 添加监控:多个
     */
    const handleAddMultiple = async () => {
      const selectedCompanies = selection.value.map((item) => {
        return {
          companyId: item.companyKeynoRelated,
          companyName: item.companyNameRelated,
          relatedType: item.relatedTypes.join(','),
          monitorGroupId: props.params.record.monitorGroupId,
        };
      });
      await addCompany(selectedCompanies);
    };

    /**
     * 添加监控:所有
     */
    const handleAddAll = async () => {
      if (pagination.value.total > MAXCOUNT) {
        message.warning(`一次最多关联监控${MAXCOUNT}家关联企业。`);
        return;
      }
      const res = await monitor.addAllRelatedMonitorCompanyRelated(prePayload.value);
      const { failCount, successCount } = res;
      if (!failCount) {
        message.success('添加成功');
      } else {
        message.warning(`添加失败${failCount}家企业,成功${successCount}家企业,请确认`);
      }

      staticChanged.value = true;
      close();
    };

    onMounted(() => {
      visible.value = true;

      initScrollListener();

      getUnMonitorCount();
    });

    onUnmounted(() => {
      removeScrollListener();
    });

    return {
      visible,
      availableData,
      selection,
      rowSelection,
      prePayload,
      unMonitorCount,
      isSubmitting,
      totalData,
      isLoading,
      handleAddMultiple,
      handleAddAll,
      close,
      handleSearch,
    };
  },
  render() {
    const { rowSelection } = this;
    return (
      <QModal
        visible={this.visible}
        size={'medium'}
        onCancel={this.close}
        dialogStyle={{ zIndex: 1040 }}
        wrapClassName={styles.modelWrapper}
      >
        <div slot="title">
          <q-entity-link
            coy-obj={{
              KeyNo: this.params.record.companyId,
              Name: this.params.record.companyName,
            }}
          ></q-entity-link>
          - 关联方变化
        </div>

        <SearchInput onSearch={this.handleSearch} onClear={this.handleSearch} style={{ marginBottom: '8px' }} />

        <QRichTable
          rowKey={'companyKeynoRelated'}
          class="infinity-list modal-table"
          columns={TABLE_COLUMNS}
          emptySize="100px"
          emptyMinHeight={'235px'}
          showIndex={false}
          loading={this.isLoading}
          dataSource={this.totalData || []}
          rowSelection={rowSelection}
          customScroll={{ x: false, y: 325 }}
          scopedSlots={{
            status: (record) => {
              return <span class="text-#999">{record.isMonitor || record.operate ? '已监控' : '未监控'}</span>;
            },
            company: (record) => {
              let companyName = escape(record?.companyNameRelated);
              if (this.prePayload.esFilter.filter.companyName) {
                companyName = companyName.replace(
                  this.prePayload.esFilter.filter.companyName,
                  `<em>${this.prePayload.esFilter.filter.companyName}</em>`
                );
              }
              const roleType = record?.relatedTypeDescList.join('，');
              const stockShare = record?.StockPercent ? `（持股比例：${record?.StockPercent}%）` : '';
              return (
                <div>
                  <div domPropsInnerHTML={companyName}></div>
                  <div class="text-#999 flex items-center justify-between">
                    <div style={{ flex: 1 }}>
                      {roleType}
                      {stockShare}
                    </div>
                    {!record?.operate ? (
                      <span
                        style={{
                          padding: '0 6px',
                          color: '#FF8900',
                          borderRadius: '2px',
                          backgroundColor: '#FFF0E0',
                        }}
                      >
                        新增
                      </span>
                    ) : (
                      <span
                        style={{
                          padding: '0 6px',
                          color: '#808080',
                          borderRadius: '2px',
                          backgroundColor: '#EEEEEE',
                        }}
                      >
                        已失效
                      </span>
                    )}
                  </div>
                </div>
              );
            },
          }}
        />

        <WarningInfo
          style={{ marginTop: '15px' }}
          text="暂不支持对注册地在中国香港、中国澳门、中国台湾及境外的企业、机关单位、个体工商户发起监控"
        />

        <template slot="footer">
          {this.unMonitorCount > 0 && hasPermission([Permission.MONITOR_ENTERPRISE_ADD_RELATED]) ? (
            <Button loading={this.isSubmitting} disabled={!this.unMonitorCount} onClick={this.handleAddAll}>
              监控全部{this.unMonitorCount}
            </Button>
          ) : null}
          <Button
            v-permission={[Permission.MONITOR_ENTERPRISE_ADD]}
            loading={this.isSubmitting}
            disabled={!this.selection?.length}
            type="primary"
            onClick={this.handleAddMultiple}
          >
            添加监控{this.selection?.length ? this.selection?.length : null}
          </Button>
        </template>
      </QModal>
    );
  },
});

export const handleOpenRelatedDetailModal = createPromiseDialog(RelatedDetailModal);
