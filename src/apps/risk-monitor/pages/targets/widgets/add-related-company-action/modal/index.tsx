import { computed, defineComponent, ref, unref, watch } from 'vue';
import { Button, message } from 'ant-design-vue';
import { differenceBy, escape, pick, uniqBy } from 'lodash';

import { useRequest } from '@/shared/composables/use-request';
import QModal from '@/components/global/q-modal';
import QRichTable from '@/components/global/q-rich-table';
import { monitor } from '@/shared/services';
import { createTrackEvent, useTrack } from '@/config/tracking-events';
import WarningInfo from '@/shared/components/warning-info';
import { useInfinityList } from '@/apps/risk-monitor/pages/targets/widgets/add-related-company-action/modal/use-infinity-list';
import { Permission } from '@/config/permissions.config';
import { hasPermission } from '@/shared/composables/use-permission';

import SearchInput from '../../search-input';
import styles from './modal.module.less';

const TABLE_COLUMNS = [
  {
    title: '企业名称',
    scopedSlots: { customRender: 'company' },
  },

  {
    title: '状态',
    width: 60,
    scopedSlots: { customRender: 'status' },
  },
];

// 最大数量
const MAXCOUNT = 6000;
const AddRelatedCompanyModal = defineComponent({
  name: 'AddRelatedCompanyModal',
  props: {
    /**
     * Item
     */
    item: {
      type: Object,
      required: true,
    },
    /**
     * Visible
     */
    visible: {
      type: Boolean,
      default: false,
    },
    /**
     * 当前分组ID
     */
    currentGroupId: {
      type: Number,
      required: true,
    },
  },
  emits: ['visibleChange', 'submit'],
  model: {
    prop: 'visible',
    event: 'visibleChange',
  },
  setup(props, { emit }) {
    const { execute, reset, data, isLoading } = useRequest(monitor.getRelatedCompany);

    const track = useTrack();
    /** 可用数据长度 */
    // .filter(({ isMonitored }) => !isMonitored));

    const pagigation = computed(() => ({
      current: data.value?.Paging.PageIndex ?? 1,
      pageSize: data.value?.Paging.PageSize ?? 5,
      total: data.value?.Paging.TotalRecords ?? 0,
    }));

    const queryParams = ref<{ companyId: string; monitorGroupId: number; keyword: string | undefined }>({
      companyId: props.item?.companyId,
      monitorGroupId: props.currentGroupId,
      keyword: undefined,
    });

    const { dataSource, removeScrollListener, initScrollListener, resetList } = useInfinityList((payload) =>
      execute({ ...queryParams.value, ...payload })
    );

    // 未监控的公司数量
    const unMonitorCount = ref(0);

    const getUnMonitorCount = async () => {
      try {
        const res = await monitor.getUnMonitorCompanyRelatedCount(pick(queryParams.value, ['companyId', 'monitorGroupId']));
        unMonitorCount.value = res.unMonitorCompanyCount;
      } catch (error) {
        console.error(error);
      }
    };

    /** 用户选择项 */
    const selection = ref<any[]>([]);
    /** Antd table 选项 */
    const rowSelection = computed(() => ({
      selectedRowKeys: selection.value.map((item) => item.companyKeynoRelated),
      checkStrictly: false,
      onChange: (selectedRowKeys, selectRow) => {
        selection.value = uniqBy(
          [...differenceBy(unref(selection), dataSource.value, 'companyKeynoRelated'), ...selectRow],
          'companyKeynoRelated'
        );
      },
      getCheckboxProps: (record) => ({
        props: {
          disabled: !!record?.isMonitor,
        },
      }),
    }));

    /** 是否可以添加监控 */
    const canAddMultiple = computed(() => {
      return rowSelection.value.selectedRowKeys.length > 0;
    });
    /**
     * 关闭窗口
     */
    const close = () => {
      emit('visibleChange', false);
    };

    /**
     * 提交表单
     */
    const submit = () => {
      emit('submit');
    };

    const isSubmitting = ref(false);

    /**
     * 添加监控
     */
    const addCompany = async (dataset) => {
      isSubmitting.value = true;
      const params =
        dataset.length === 0
          ? {
              companyId: props.item?.companyId,
              monitorGroupId: props.currentGroupId,
              count: MAXCOUNT,
            }
          : {
              items: dataset,
              companyId: props.item?.companyId,
              monitorGroupId: props.currentGroupId,
            };
      try {
        await monitor.addRelatedCompany(params);
        isSubmitting.value = false;

        message.success('添加成功');
        submit();
        close();
      } catch (error) {
        isSubmitting.value = false;
      }
    };

    /**
     * 添加监控:多个
     */
    const handleAddMultiple = async () => {
      if (selection.value.length > MAXCOUNT) {
        message.warning(`一次最多关联监控${MAXCOUNT}家关联企业。`);
        return;
      }
      const selectedCompanies = selection.value.map(({ companyKeynoRelated, companyNameRelated, relatedTypes }) => {
        return {
          companyId: companyKeynoRelated,
          companyName: companyNameRelated,
          relatedType: relatedTypes.join(','),
          monitorGroupId: props.currentGroupId,
        };
      });
      await addCompany(selectedCompanies);
      track(createTrackEvent(6975, '监控列表', '添加监控'));
    };

    /**
     * 添加监控:所有
     */
    const handleAddAll = async () => {
      if (pagigation.value.total > MAXCOUNT) {
        message.warning(`一次最多关联监控${MAXCOUNT}家关联企业。`);
        return;
      }
      try {
        const res = await monitor.monitorAllRelated(pick(queryParams.value, ['companyId', 'monitorGroupId']));
        const { failCount, successCount } = res;
        if (!failCount) {
          message.success('添加成功');
        } else {
          message.warning(`添加失败${failCount}家企业,成功${successCount}家企业,请确认`);
        }
        track(createTrackEvent(6975, '监控列表', '监控全部'));
        submit();
        close();
      } catch (error) {
        console.log(error);
      }
    };

    const handleSearch = (keyword: string) => {
      if (queryParams.value.keyword === keyword) return;
      queryParams.value.keyword = keyword;
      resetList();
    };

    /** 监听弹窗显隐执行或重置数据 */
    watch(
      () => props.visible,
      (isVisible: boolean) => {
        if (isVisible) {
          initScrollListener();
          getUnMonitorCount();
        } else {
          removeScrollListener();
          selection.value = [];
          queryParams.value.keyword = undefined;
          reset();
        }
      }
    );

    return {
      data,
      isLoading,
      selection,
      rowSelection,
      queryParams,
      pagigation,
      close,
      execute,
      canAddMultiple,
      handleAddMultiple,
      handleAddAll,
      isSubmitting,
      unMonitorCount,
      handleSearch,
      dataSource,
    };
  },
  render() {
    const { isLoading, rowSelection } = this;

    return (
      <QModal
        {...{
          props: {
            // title,
            visible: this.visible,
            destroyOnClose: true,
            width: 600,
          },
          on: {
            cancel: this.close,
          },
        }}
      >
        <div class={styles.emphasis} slot="title">
          <a
            href={`/embed/companyDetail?keyNo=${this.item.companyId}&title=${this.item.companyName}`}
            target="_blank"
            domPropsInnerHTML={this.item.companyName}
          ></a>
          - 添加关联方
        </div>
        <div>
          <SearchInput onSearch={this.handleSearch} onClear={this.handleSearch} style={{ marginBottom: '8px' }} />
          <QRichTable
            class="infinity-list modal-table"
            rowKey={'companyKeynoRelated'}
            columns={TABLE_COLUMNS}
            emptySize="100px"
            emptyMinHeight={'295px'}
            showIndex={true}
            loading={isLoading}
            dataSource={this.dataSource}
            rowSelection={rowSelection}
            customScroll={{ x: false, y: this.dataSource.length ? 328 : false }}
            scopedSlots={{
              status: (record) => {
                return <span class="text-#999">{record.isMonitor ? '已监控' : '未监控'}</span>;
              },
              company: (record) => {
                const companyName = escape(record?.companyNameRelated).replace(
                  this.queryParams.keyword ?? '',
                  `<em>${this.queryParams.keyword ?? ''}</em>`
                );
                const roleType = record?.relatedTypeDescList.join('，');
                const stockShare = record?.StockPercent ? `（持股比例：${record?.StockPercent}%）` : '';
                return (
                  <div class={styles.emphasis}>
                    <div domPropsInnerHTML={companyName}></div>
                    <div class="text-#999">
                      {roleType}
                      {stockShare}
                    </div>
                  </div>
                );
              },
            }}
          />
          <WarningInfo
            style={{ marginTop: '15px' }}
            text="暂不支持对注册地在中国香港、中国澳门、中国台湾及境外的企业、机关单位、个体工商户发起监控"
          />
        </div>

        <template slot="footer">
          {/* 凑数的按钮，监控全部按钮加上后可去除 */}
          {this.unMonitorCount && hasPermission([Permission.MONITOR_ENTERPRISE_ADD_RELATED]) ? (
            <Button loading={this.isSubmitting} onClick={this.handleAddAll}>
              监控全部{this.unMonitorCount || ''}
            </Button>
          ) : null}
          <Button
            v-permission={[Permission.MONITOR_ENTERPRISE_ADD]}
            loading={this.isSubmitting}
            disabled={!this.canAddMultiple}
            type="primary"
            onClick={this.handleAddMultiple}
          >
            添加监控 {this.selection.length || ''}
          </Button>
        </template>
      </QModal>
    );
  },
});

export default AddRelatedCompanyModal;
