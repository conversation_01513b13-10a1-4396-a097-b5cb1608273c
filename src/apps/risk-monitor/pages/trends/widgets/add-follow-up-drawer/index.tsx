import { computed, defineComponent, onMounted, reactive, ref } from 'vue';
import { <PERSON><PERSON>, Drawer, FormModel, Input, message, Pagination, Spin } from 'ant-design-vue';

import { monitor as monitorService } from '@/shared/services';
import { createPromiseDialog } from '@/components/promise-dialogs';
import QEntityLink from '@/components/global/q-entity-link';
import { useSearch } from '@/shared/composables/use-search';
import { useStore } from '@/store';
import { FollowUpCheckOptipons, FollowUpLevelOptipons, FollowUpStatusOptions } from '@/config/risk.config';

import FollowUpList from './widgets/follow-up-list';
import './add-follow-up-drawer.less';

const AddFollowUp = defineComponent({
  name: 'AddFollowUp',
  props: {
    params: {
      type: Object,
      default: () => ({}),
    },
  },
  setup(props) {
    const store = useStore();
    const operatorList = computed(() => store.state.user.personList);
    const visible = ref(false);
    const pagination = reactive({
      current: 1,
      pageSize: 10,
      total: 0,
      pageSizeOptions: ['5', '10', '20'],
    });
    const baseQuery = {
      dynamicId: props.params.record.uniqueHashkey,
    };

    const followUpList = ref([]);
    const isFinished = computed(() => props.params.record.status === 1);

    const form = ref({
      grade: 0,
      way: 1,
      comment: '',
      attachments: [],
      status: 2,
    });

    const getFollowList = async () => {
      const params = { ...baseQuery, pageSize: pagination.pageSize, pageIndex: pagination.current };
      const res = await monitorService.getFollowUpList(params);
      followUpList.value = res.data;
      pagination.total = res.total;
    };

    const { search, isLoading } = useSearch(getFollowList);

    search();

    const pageChange = (current, pageSize) => {
      pagination.current = current;
      pagination.pageSize = pageSize;
      search();
    };

    const update = () => {
      if (Object.values(form.value).filter((item) => item).length <= 1) {
        visible.value = false;
        return;
      }

      monitorService.addFollowUp({ ...form.value, ...baseQuery }).then(() => {
        message.success('处理成功');
        visible.value = false;
        if (props.params.handleOk) {
          props.params.handleOk?.();
        }
      });
    };

    onMounted(async () => {
      visible.value = true;
      store.dispatch('user/fetchPersonList');
    });

    return {
      visible,
      form,
      update,
      pagination,
      pageChange,
      search,
      isLoading,
      isFinished,
      followUpList,
      operatorList,
    };
  },
  render() {
    return (
      <Drawer
        width="600px"
        wrapClassName={'addfollowup'}
        {...{
          props: {
            visible: this.visible,
            destroyOnClose: true,
            closable: false,
          },
        }}
        onClose={() => {
          this.visible = false;
        }}
      >
        <div slot="title" class="drawer-title">
          跟进
          <q-icon
            class="icon-close"
            type="icon-tanchuangguanbi"
            onClick={() => {
              this.visible = false;
            }}
          ></q-icon>
        </div>
        <Spin spinning={this.isLoading}>
          <div style={{ padding: '60px 0' }}>
            <section class="follow-up-target">
              <div class="follow-up-list-title">跟进对象</div>
              <div class="follow-up-content">
                <div class="follow-up-content-item flex">
                  <div class="follow-up-content-item-title">监控对象</div>
                  <div class="follow-up-content-item-content">
                    <QEntityLink
                      coy-obj={{
                        KeyNo: this.params.record.companyId,
                        Name: this.params.record.companyName,
                      }}
                    ></QEntityLink>
                  </div>
                </div>
                <div class="follow-up-content-item flex">
                  <div class="follow-up-content-item-title">风险指标</div>
                  <div class="follow-up-content-item-content">{this.params.record.metricsName}</div>
                </div>
              </div>
            </section>
            {!this.isFinished && (
              <section class="follow-up-target">
                <div class="follow-up-list-title">跟进内容</div>
                <div class="follow-up-content" style="padding: 5px 10px 1px">
                  <FormModel
                    props={{ model: this.form }}
                    label-col={{ span: 2 }}
                    colon={false}
                    wrapper-col={{ span: 22 }}
                    labelAlign="left"
                  >
                    <FormModel.Item label="跟进等级">
                      <q-select allowClear={false} v-model={this.form.grade} options={FollowUpLevelOptipons} placeholder="请选择跟进等级" />
                    </FormModel.Item>
                    <FormModel.Item label="核实方式">
                      <q-select allowClear={false} v-model={this.form.way} options={FollowUpCheckOptipons} placeholder="请选择核实方式" />
                    </FormModel.Item>
                    <FormModel.Item label="处理结果">
                      <div class="relative">
                        <Input.TextArea
                          ref="textareaRef"
                          style={{ minHeight: '100px', padding: '8px 10px' }}
                          placeholder="请输入您的备注信息"
                          autosize={{ minRows: 4, maxRows: 8 }}
                          maxLength={200}
                          v-model={this.form.comment}
                        ></Input.TextArea>
                        <div class="text-14px text-#999 absolute" style={{ right: '8px', bottom: '3px' }}>
                          <span style={{ color: this.form.comment?.length === 200 ? '#F04040' : 'inherit' }}>
                            {this.form.comment?.length}
                          </span>
                          {` / `}
                          <span>{200}</span>
                        </div>
                      </div>
                    </FormModel.Item>
                    {/* <FormModel.Item label="附件">
                      <UploadFileNew class="upload-file" v-model={this.form.attachments} />
                    </FormModel.Item> */}
                    <FormModel.Item label="跟进状态">
                      <q-select
                        allowClear={false}
                        v-model={this.form.status}
                        options={FollowUpStatusOptions}
                        placeholder="请选择跟进状态"
                      />
                    </FormModel.Item>
                  </FormModel>
                </div>
              </section>
            )}

            {this.params.record.status !== 1 && (
              <div class="footBtn">
                <Button
                  style={{ margin: '0 15px 0' }}
                  onClick={() => {
                    this.visible = false;
                  }}
                >
                  取消
                </Button>
                <Button type="primary" onClick={() => this.update()}>
                  确定
                </Button>
              </div>
            )}

            {this.followUpList?.length > 0 && (
              <div class="follow-up-list">
                <FollowUpList list={this.followUpList} status={this.params.record.status} operatorList={this.operatorList} />
                {this.pagination.total > this.pagination.pageSize ? (
                  <div class="pagination">
                    <Pagination
                      {...{ props: this.pagination }}
                      showSizeChanger={true}
                      onChange={this.pageChange}
                      onShowSizeChange={this.pageChange}
                    />
                  </div>
                ) : null}
              </div>
            )}
          </div>
        </Spin>
      </Drawer>
    );
  },
});

export default AddFollowUp;
export const openFollowUpDrawer = createPromiseDialog(AddFollowUp);
