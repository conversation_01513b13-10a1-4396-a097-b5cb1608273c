import { PropType, defineComponent } from 'vue';
import moment from 'moment';

import { FollowUpCheckOptipons, FollowUpLevelOptipons, FollowUpStatusOptions } from '@/config/risk.config';

import styles from './follow-up-list.module.less';

const GRADEMAP = FollowUpLevelOptipons.reduce((map, item) => {
  map[item.value] = item.label;
  return map;
}, {});

const ChECKWAYMAP = FollowUpCheckOptipons.reduce((map, item) => {
  map[item.value] = item.label;
  return map;
}, {});
const FollowUpList = defineComponent({
  name: 'FollowUpList',
  props: {
    list: {
      type: Array as PropType<any[]>,
      default: () => [],
    },
    // 当前动态的跟进状态
    status: {
      type: Number,
      default: 2,
    },
    operatorList: {
      type: Array as PropType<{ userId: number; name: string }[]>,
      default: () => [],
    },
  },
  setup(props) {
    /**
     * 如果已处理，最后一条显示已处理，其余跟进中
     * 如果跟进中，全都是跟进中
     * @param index props.list的下标
     * @returns
     */
    const getStatus = (index) => {
      return props.status === 1 && index === 0 ? FollowUpStatusOptions[1].label : FollowUpStatusOptions[0].label;
    };

    const getOperator = (id) => {
      return props.operatorList.find((v) => v.userId === id)?.name || '-';
    };
    return {
      getStatus,
      getOperator,
    };
  },
  render() {
    return (
      <div>
        <div class="follow-up-list-title">跟进记录</div>
        {this.list.map((item, index) => {
          return (
            <div data-testid="follow-up-list-item" class={styles.historyItemWrapper} key={item.id}>
              <div class={styles.step}>
                <div class={styles.dot}></div>
                <div class={styles.line}></div>
              </div>
              <div class={styles.content}>
                <div class="flex" style={{ marginBottom: '10px' }}>
                  <span>{moment(item.createDate).format('YYYY-MM-DD HH:mm:ss')}</span>
                  <span class="text-#999" style={{ margin: '0 5px' }}>
                    操作人：{this.getOperator(item.updateBy)}
                  </span>
                  <span>{this.getStatus(index)}</span>
                </div>
                <div
                  style={{
                    padding: '10px',
                    borderRadius: '4px',
                    backgroundColor: '#fafafa',
                    wordBreak: 'break-all',
                    gap: '5px',
                  }}
                >
                  <div class={styles.form}>
                    <div class={styles.formLabel}>跟进等级：</div>
                    <div class={styles.formItem}>{GRADEMAP[item.grade] || '-'}</div>
                  </div>
                  <div class={styles.form}>
                    <div class={styles.formLabel}>核实方式：</div>
                    <div class={styles.formItem}>{ChECKWAYMAP[item.way] || '-'}</div>
                  </div>
                  <div class={styles.form}>
                    <div class={styles.formLabel}>处理结果：</div>
                    <div class={styles.formItem}>{item.comment || '-'}</div>
                  </div>
                  {/* <div class={styles.form}>
                    <div class={styles.formLabel}>附件：</div>
                    <div class={[styles.formItem]}>
                      <div class="flex flex-col" style={{ gap: '5px' }}>
                        {item?.attachments?.length
                          ? item?.attachments?.map((file) => {
                              return (
                                <div
                                  data-testid="follow-up-list-file"
                                  class={[styles.file, 'flex items-center']}
                                  key={file.fileUrl}
                                >
                                  <FileIcon style={{ marginLeft: '-3px' }} type={file?.fileName?.split('.')?.pop()} />
                                  <span style={{ marginLeft: '5px' }}>{file.fileName || 'file'}</span>
                                  <Tooltip title="下载附件" placement="bottom">
                                    <a href={file.fileUrl} style={{ marginLeft: '10px' }}>
                                      <QIcon type="icon-xiazai"></QIcon>
                                    </a>
                                  </Tooltip>
                                </div>
                              );
                            })
                          : '-'}
                      </div>
                    </div>
                  </div> */}
                </div>
              </div>
            </div>
          );
        })}
      </div>
    );
  },
});

export default FollowUpList;
