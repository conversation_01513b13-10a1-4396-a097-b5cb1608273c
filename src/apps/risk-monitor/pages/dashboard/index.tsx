import { computed, defineComponent, onMounted, reactive, ref } from 'vue';
import { useRouter } from 'vue-router/composables';
import { Spin } from 'ant-design-vue';
import { get } from 'lodash';

import Pie<PERSON>hart from '@/apps/risk-monitor/pages/dashboard/widgets/pie-chart';
import Heatmap<PERSON>hart from '@/apps/risk-monitor/pages/dashboard/widgets/heatmap-chart';
import EmptyBlock from '@/apps/risk-monitor/pages/dashboard/widgets/empty-block';
import { monitor as monitorService } from '@/shared/services';
import { useRequest } from '@/shared/composables/use-request';
import HeroicLayout from '@/shared/layouts/heroic';
import { MetricTypeEnums } from '@/config/risk.config';
import Empty from '@/shared/components/empty';
import QGlossaryInfo from '@/components/global/q-glossary-info';
import { Permission } from '@/config/permissions.config';

import GroupSelect from './widgets/group-select';
import CardBlock, { RiskTrends } from './widgets/card-block';
import styles from './dashboard.module.less';

const AggsTopN = 10;

const RiskMonitorDashboardPage = defineComponent({
  name: 'RiskMonitorDashboardPage',
  setup() {
    const router = useRouter();

    const rawHeatmapData = ref<any[]>([]);

    const baseQuery = {
      createDate: [{ currently: true, flag: 1, number: 1, unit: 'day' }],
    };

    const currentPieGroupId = ref<number[]>();
    const currentHeatmapGroupId = ref<number[]>();
    const groupList = ref<any[]>([]);
    const favoriteGroup = reactive<{ id?: number; name?: string }>({
      id: undefined,
      name: undefined,
    });
    const fetchOverviewData = async () => {
      if (!favoriteGroup.id) {
        // TODO 此处重点关注分组随便取的，后续迭代优化
        const { data: groupData = [] } = await monitorService.getAllGroups({ pageIndex: 1, pageSize: 100 });
        groupList.value = groupData.map((item) => ({ ...item, label: item.name, value: item.monitorGroupId }));
        favoriteGroup.id = groupData[0]?.monitorGroupId;
        favoriteGroup.name = groupData[0]?.name;
      }

      const [card1Data, card2Data] = await Promise.all([
        // 今日动态统计数据 & 维度数据
        monitorService.getMonitorOverviewData({ aggsField: [1, 4], ...baseQuery }),
        // 监控企业的数据
        monitorService.getMonitorOverviewData({ aggsField: [2], favoriteGroupId: favoriteGroup.id }),
      ]);

      return {
        cardData: { ...card1Data, ...card2Data },
        metricsList: card1Data?.['4_metricsId']?.buckets?.map((metricBucket) => ({
          metricsId: +metricBucket.key,
          name: get(metricBucket, 'metricsName.buckets[0].key'),
        })),
      };
    };

    const fetchPieData = async (groupId) => {
      currentPieGroupId.value = groupId;
      const res = await monitorService.getMonitorOverviewData({
        aggsField: [3],
        aggsTopN: AggsTopN,
        groupId,
        ...baseQuery,
      });
      return res['3_distributionCard']?.buckets || [];
    };
    const pieRequest = useRequest(fetchPieData);

    const { execute, data: dataSource, isLoading } = useRequest(fetchOverviewData);

    // 卡片1数据
    const todayData = computed(() => {
      const data = dataSource.value?.cardData || {};
      const types = data['1_todayDynamicCardByTypes']?.buckets || [];
      return {
        total: data['1_todayDynamicCardTotal']?.value || 0,
        monitor: types.find((v) => v.key === MetricTypeEnums.MonitorSupervisionMetric)?.doc_count || 0,
        business: types.find((v) => v.key === MetricTypeEnums.MonitorBusinessMetric)?.doc_count || 0,
      };
    });

    // 卡片2数据
    const monitorData = computed(() => {
      const data = dataSource.value?.cardData || {};
      return {
        total: data['2_monitorCompanyCardTotal'] || 0,
        follow: data['2_monitorCompanyCardAwaitingTracking']?.companyCount.value || 0,
        focusGroupName: favoriteGroup.name,
        focus: data['2_monitorCompanyCardFavoriteGroup']?.companyCount.value || 0,
      };
    });

    const isDataEmpty = computed(() => monitorData.value.total === 0);

    // 饼图数据
    const pieData = computed(() => {
      const data = pieRequest.data.value || [];
      const metricsList = dataSource.value?.metricsList || [];
      return data.map((item) => {
        const name = metricsList.find((v) => v.metricsId === Number(item.key))?.name || item.key;
        return {
          name,
          value: item.doc_count,
          metricId: item.key,
        };
      });
    });

    // 热力图XY轴对应metricsId
    const axisDataIds = ref<number[]>([]);

    // 热力图XY轴对应名称
    const axisDataLabels = ref<string[]>([]);

    const fetchHighRiskDimension = async (groupId, minCount = 10) => {
      if (!groupId) return;
      // 今日高风险动态分析热力图
      const res: Readonly<{
        metricsIds: string[];
        metricEntities: { metricsId: number; name: string }[];
        highRiskMetrics: { metricsId: number; name: string }[];
      }> = await monitorService.getChartSummary({
        riskLevels: [2],
        groupId,
        ...baseQuery,
      });
      if (!res) return;
      axisDataIds.value = res.metricEntities.map((item) => item.metricsId) || [];
      const labels = res.metricEntities.map((item) => item.name) || [];
      // 补齐空白维度
      if (labels.length < minCount) {
        const others = res.highRiskMetrics.filter((v) => !labels.includes(v.name)).slice(0, minCount - labels.length);
        labels.push(...others.map((v) => v.name));
      }
      axisDataLabels.value = labels;
      return res;
    };

    const fetchHeatmapData = async (groupId) => {
      currentHeatmapGroupId.value = groupId;
      rawHeatmapData.value = [];
      await fetchHighRiskDimension(currentHeatmapGroupId.value);
      const axis = axisDataIds.value;
      // x.length * y.length <= max
      const max = 80;
      const xLen = axis.length;
      const yLen = Math.ceil(max / xLen);
      Array.from({ length: Math.ceil(xLen / yLen) }, (_, i) => {
        return monitorService
          .getHighRiskChart({
            xAxis: axis,
            yAxis: axis.slice(i * yLen, i * yLen + yLen),
            filter: { groupId, ...baseQuery },
          })
          .then(({ items = [] }) => {
            rawHeatmapData.value.push(...items);
          });
      });
    };
    const heatMapRequest = useRequest(fetchHeatmapData);

    // 热力图数据
    const heatMapData = computed(() => {
      const data = rawHeatmapData.value;
      if (!data.length) return [];
      const map = new Map();
      data.forEach((item) => {
        const { xAxis, yAxis, count } = item;
        map.set(`${xAxis},${yAxis}`, count);
      });
      const result: any[] = [];
      axisDataIds.value.forEach((xAxis, i) => {
        axisDataIds.value.forEach((yAxis, j) => {
          const count = map.get(`${xAxis},${yAxis}`) || 0;
          result.push([i, j, count]);
        });
      });
      return result;
    });

    const handlePieClick = ({ metricId }) => {
      router.push({
        path: '/risk-monitor/trends',
        query: {
          metricsIds: metricId,
          createDate: JSON.stringify(baseQuery.createDate[0]),
          groupId: currentPieGroupId.value?.[0]?.toString(),
        },
      });
    };

    const handleCardClick = (type) => {
      const query: Record<string, any> = {};
      if (type === 'company') {
        router.push({
          path: '/risk-monitor/targets',
        });
        return;
      }
      switch (type) {
        case 'focus':
          query.groupId = favoriteGroup.id;
          break;
        case 'follow':
          query.dataStatus = 2;
          break;
        case 'monitor':
          query.metricTypes = MetricTypeEnums.MonitorSupervisionMetric;
          query.createDate = JSON.stringify(baseQuery.createDate[0]);
          break;
        case 'business':
          query.metricTypes = MetricTypeEnums.MonitorBusinessMetric;
          query.createDate = JSON.stringify(baseQuery.createDate[0]);
          break;
        case 'trends':
          query.createDate = JSON.stringify(baseQuery.createDate[0]);
          break;
        default:
      }
      router.push({
        path: '/risk-monitor/trends',
        query,
      });
    };

    onMounted(async () => {
      await execute();
      pieRequest.execute([favoriteGroup.id]);
      heatMapRequest.execute([favoriteGroup.id]);
    });

    return {
      isDataEmpty,
      dataSource,
      execute,
      isLoading,
      pieData,
      todayData,
      monitorData,
      axisDataLabels,
      heatMapData,
      handlePieClick,
      handleCardClick,
      currentPieGroupId,
      currentHeatmapGroupId,
      pieRequest,
      heatMapRequest,
      groupList,
    };
  },
  render() {
    if (this.isLoading) {
      return (
        <div class={[styles.container, styles.bg, styles.loading]}>
          <Spin spinning />
        </div>
      );
    }
    return (
      <div class={[styles.container, this.isDataEmpty ? '' : styles.bg]}>
        <HeroicLayout bodyStyle={{ background: 'transparent' }}>
          <h1 slot="hero" class={styles.title}>
            {this.$route?.meta?.title}
          </h1>
          {this.isDataEmpty ? (
            <EmptyBlock />
          ) : (
            <div class={styles.content}>
              <div class={styles.cardContainer}>
                <RiskTrends
                  title="今日动态"
                  permissions={[Permission.RISK_TRENDS_VIEW]}
                  dataSource={this.todayData}
                  onGoDetail={this.handleCardClick}
                />
                <RiskTrends
                  permissions={[Permission.MONITOR_ENTERPRISE_VIEW]}
                  title="监控企业"
                  dataSource={this.monitorData}
                  type="company"
                  onGoDetail={this.handleCardClick}
                />
                <CardBlock>
                  <div slot={'title'}>
                    <span>今日风险动态分布</span>
                    <span class={styles.top10}>动态Top{AggsTopN}指标</span>
                  </div>
                  <div slot={'extra'}>
                    <GroupSelect defaultValue={this.currentPieGroupId} options={this.groupList} onChange={this.pieRequest.execute} />
                  </div>
                  <div class={styles.pieContainer} style={{ padding: '13px 0' }}>
                    {this.pieData.length > 0 ? (
                      <PieChart data={this.pieData} onClick={this.handlePieClick} />
                    ) : (
                      <Empty type="text" description="今日无风险动态" />
                    )}
                  </div>
                </CardBlock>
              </div>
              <CardBlock>
                <div slot={'title'}>
                  <span>今日高风险动态分析</span>
                  <QGlossaryInfo tooltip="格子数值表示同时命中X/Y轴的高风险指标下的企业数量" />
                </div>
                <div slot={'extra'}>
                  <GroupSelect defaultValue={this.currentHeatmapGroupId} options={this.groupList} onChange={this.heatMapRequest.execute} />
                </div>
                <div class={styles.pieContainer} style={{ height: '612px', padding: '8px 0' }}>
                  {this.heatMapData.length > 0 ? (
                    <HeatmapChart data={this.heatMapData} xAxisData={this.axisDataLabels} yAxisData={this.axisDataLabels} />
                  ) : (
                    <Empty type="text" description="今日无高风险动态" />
                  )}
                </div>
              </CardBlock>
            </div>
          )}
        </HeroicLayout>
      </div>
    );
  },
});

export default RiskMonitorDashboardPage;
