// Vitest Snapshot v1, https://vitest.dev/guide/snapshot.html

exports[`RiskMonitorDashboardPage > renders correctly 1`] = `
<div class="container bg">
  <hero-layout bodystyle="[object Object]">
    <h1 class="title"></h1>
    <div class="content">
      <div class="cardContainer">
        <div class="card">
          <div class="body">
            <div class="defaultHeader" style="cursor: normal;">
              <div class="header">
                <div class="title">今日动态</div>
                <div class="riskIcon blue"><img src="/src/apps/risk-monitor/pages/dashboard/widgets/card-block/assets/risk-warning.svg"></div>
              </div>
              <div class="overview"><span class="count">7</span></div>
            </div>
            <ul class="list">
              <li class="item" style="cursor: normal;"><span class="title">监管类动态</span>
                <div class="info"><span class="count">1</span></div>
              </li>
              <li class="item" style="cursor: normal;"><span class="title">业务类动态</span>
                <div class="info"><span class="count">6</span></div>
              </li>
            </ul>
          </div>
        </div>
        <div class="card">
          <div class="body">
            <div class="defaultHeader" style="cursor: normal;">
              <div class="header">
                <div class="title">监控企业</div>
                <div class="riskIcon orange"><img src="/src/apps/risk-monitor/pages/dashboard/widgets/card-block/assets/risk-company.svg"></div>
              </div>
              <div class="overview"><span class="count">10</span></div>
            </div>
            <ul class="list">
              <li class="item" style="cursor: normal;"><span class="title">跟进中企业</span>
                <div class="info"><span class="count">0</span></div>
              </li>
              <li class="item" style="cursor: normal;"><span class="title">重点关注分组：Group 1</span>
                <div class="info"><span class="count">1</span></div>
              </li>
            </ul>
          </div>
        </div>
        <div class="card">
          <div class="header">
            <div class="title">
              <div><span>今日风险动态分布</span><span class="top10">动态Top10指标</span></div>
            </div>
            <div class="extra">
              <div>
                <div><span style="color: #999;">当前组别：</span>
                  <select-stub defaultactivefirstoption="true" prefixcls="ant-select" transitionname="slide-up" optionlabelprop="children" optionfilterprop="value" choicetransitionname="zoom" placeholder="请选择" value="Group 1" dropdownstyle="[object Object]" tokenseparators="" showaction="click" clearicon="[object Object]" inputicon="[object Object]" removeicon="[object Object]" menuitemselectedicon="[object Object]" dropdownrender="[Function]" dropdownmatchselectwidth="true" dropdownmenustyle="[object Object]" notfoundcontent="[object Object]" tabindex="0" autoclearsearchvalue="true" __propssymbol__="Symbol()" children="[object Object]" class="defaultClass" style="min-width: 160px; width: 150px;"></select-stub>
                </div>
              </div>
            </div>
          </div>
          <div class="body">
            <div class="pieContainer" style="padding: 13px 0px;">
              <<div class="pie-chart" /> data="[object Object],[object Object]"></<div class="pie-chart" />>
            </div>
          </div>
        </div>
      </div>
      <div class="card">
        <div class="header">
          <div class="title">
            <div><span>今日高风险动态分析</span>
              <div class="container inline">
                <atooltip-stub trigger="hover" placement="bottom" transitionname="zoom-big" overlaystyle="[object Object]" prefixcls="ant-popover" mouseenterdelay="0.1" mouseleavedelay="0.1" autoadjustoverflow="true" align="[object Object]"><template>
                    <div>
                      <div class="ant-popover-inner-content">
                        <div class="content">格子数值表示同时命中X/Y轴的高风险指标下的企业数量</div>
                      </div>
                    </div>
                  </template>
                  <div class="trigger trigger">
                    <q-icon-stub type="icon-a-shuomingxian"></q-icon-stub>
                  </div>
                </atooltip-stub>
              </div>
            </div>
          </div>
          <div class="extra">
            <div>
              <div><span style="color: #999;">当前组别：</span>
                <select-stub defaultactivefirstoption="true" prefixcls="ant-select" transitionname="slide-up" optionlabelprop="children" optionfilterprop="value" choicetransitionname="zoom" placeholder="请选择" value="Group 1" dropdownstyle="[object Object]" tokenseparators="" showaction="click" clearicon="[object Object]" inputicon="[object Object]" removeicon="[object Object]" menuitemselectedicon="[object Object]" dropdownrender="[Function]" dropdownmatchselectwidth="true" dropdownmenustyle="[object Object]" notfoundcontent="[object Object]" tabindex="0" autoclearsearchvalue="true" __propssymbol__="Symbol()" children="[object Object]" class="defaultClass" style="min-width: 160px; width: 150px;"></select-stub>
              </div>
            </div>
          </div>
        </div>
        <div class="body">
          <div class="pieContainer" style="height: 612px; padding: 8px 0px;">
            <div class="container vertical" style="height: 100%;">
              <div class="content"><img src="/src/shared/components/assets/images/icon_empty_text.svg" width="100" height="100">
                <div class="description">今日无高风险动态</div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  </hero-layout>
</div>
`;
