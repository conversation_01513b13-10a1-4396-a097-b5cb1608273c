import { computed, defineComponent, nextTick, PropType, ref } from 'vue';
import { use } from 'echarts/core';
import { CanvasRenderer } from 'echarts/renderers';
import { HeatmapChart as VHeatmapChart } from 'echarts/charts';
import { TitleComponent, TooltipComponent } from 'echarts/components';
import VChart from 'vue-echarts';

import styles from './heatmap-chart.module.less';
import { useChartResize } from '@/apps/risk-monitor/pages/dashboard/hooks/use-chart-resize';
import { useLabelTooltip } from '@/apps/risk-monitor/pages/dashboard/hooks/use-label-tooltip';

use([CanvasRenderer, VHeatmapChart, TitleComponent, TooltipComponent]);

const getLabelWidth = (
  labels: string[],
  options = {
    offsetRight: 4,
    offsetLeft: 8,
    fontSize: 12,
    maxLen: 13,
  }
) => {
  const { fontSize, offsetLeft, offsetRight, maxLen } = options;
  const maxLabelLength = labels.reduce((prev, curr) => {
    return Math.max(prev, curr.length);
  }, 0);
  const labelWidth = (maxLabelLength > maxLen ? maxLen : maxLabelLength) * fontSize;
  return {
    labelWidth,
    gridWidth: labelWidth + offsetLeft + offsetRight + fontSize,
    nameGap: labelWidth + offsetRight,
  };
};

const getChartOption = (data: any[], xAxisData: string[], yAxisData: string[]) => {
  const { nameGap, labelWidth, gridWidth } = getLabelWidth(yAxisData);
  const chartOption = {
    tooltip: {
      borderWidth: 0,
      extraCssText: `box-shadow: 0px 2px 4px 0px rgba(0, 0, 0, 0.2); padding: 8px 12px;`,
      formatter: (params: any) => {
        const containerCss = `display: flex; flex-direction: column; gap: 8px; color: #333; font-size: 14px; line-height: 22px;`;
        const flex = `display: flex; align-items: center;gap: 4px;`;
        const dotCss = `width: 8px; height: 8px; border-radius: 50%; background-color: ${params.color};`;
        const xLabel = xAxisData[params.data[0]];
        const yLabel = yAxisData[params.data[1]];
        const value = params.value[2];
        return `
        <div style="${containerCss}">
          <div>企业数量：<span style="color: #808080;">${value}</span></div>
          <div style="${flex}"><div style="${dotCss}"></div>X：${xLabel}</div>
          <div style="${flex}"><div style="${dotCss}"></div>Y：${yLabel}</div>
        </div>
        `;
      },
    },
    grid: {
      top: 0,
      left: gridWidth,
      bottom: 140,
      right: 90,
      // height: '50%',
      // top: '10%',
    },
    xAxis: {
      name: '高风险指标',
      nameLocation: 'center',
      nameGap: 124,
      nameTextStyle: {
        color: '#3d3d3d',
        fontSize: 12,
      },
      nameTruncate: {
        maxWidth: 100,
        ellipsis: '...',
      },
      type: 'category',
      data: xAxisData,
      splitArea: {
        show: true,
      },
      axisTick: {
        show: false,
        alignWithLabel: true,
      },
      axisLabel: {
        width: labelWidth,
        overflow: 'truncate',
        rotate: -45,
        color: '#333',
        margin: 7,
      },
      axisLine: {
        lineStyle: {
          color: '#d8d8d8',
          width: 1,
          opacity: 1,
        },
      },
    },
    yAxis: {
      name: '高风险指标',
      nameLocation: 'center',
      nameGap,
      nameRotate: 90,
      nameTextStyle: {
        color: '#3d3d3d',
        fontSize: 12,
      },
      nameTruncate: {
        maxWidth: 100,
        ellipsis: '...',
      },
      type: 'category',
      data: yAxisData,
      axisTick: {
        show: false,
        alignWithLabel: true,
      },
      splitArea: {
        show: true,
      },
      axisLabel: {
        color: '#333',
        margin: 4,
        width: labelWidth,
        overflow: 'truncate',
      },
      axisLine: {
        lineStyle: {
          color: '#d8d8d8',
          width: 1,
          opacity: 1,
        },
      },
    },
    visualMap: {
      calculable: true,
      orient: 'vertical',
      right: 0,
      bottom: 134,
      type: 'piecewise',
      textGap: 10,
      itemGap: 1,
      itemSymbol: 'rect',
      itemWidth: 20,
      itemHeight: 32,
      align: 'left',
      hoverLink: false,
      pieces: [
        { min: 81, label: '81-99+', color: '#7B0000' },
        { min: 60, max: 80, label: '61-80', color: '#B30000' },
        { min: 41, max: 60, label: '41-60', color: '#D61F1F' },
        { min: 21, max: 40, label: '21-40', color: '#F46F6F' },
        { min: 1, max: 20, label: '1-20', color: '#FFD2D2' },
        { max: 0, label: '-', color: '#f3f3f3' }, // 不指定 min，表示 min 为无限大（-Infinity）。
      ],
      outOfRange: {
        color: '#d8d8d8',
        opacity: 0,
      },
    },
    series: [
      {
        data,
        name: '高风险指标',
        type: 'heatmap',
        label: {
          show: true,
          formatter: (params: any) => {
            const value = params.value[2];
            const max = value >= 100 ? '99+' : undefined;
            const min = value <= 0 ? '-' : value;
            return max ?? min;
          },
        },
        itemStyle: {
          borderColor: '#fff',
          borderWidth: 1,
        },
        emphasis: {
          itemStyle: {
            shadowColor: 'rgba(0, 0, 0, 0.15)',
            shadowBlur: 10,
          },
        },
      },
    ],
  };
  return Object.freeze(chartOption);
};

const HeatmapChart = defineComponent({
  name: 'HeatmapChart',
  props: {
    data: {
      type: Array as PropType<any[]>,
      required: true,
    },
    xAxisData: {
      type: Array as PropType<string[]>,
      required: true,
    },
    yAxisData: {
      type: Array as PropType<string[]>,
      required: true,
    },
    width: {
      type: String,
      default: '100%',
    },
    height: {
      type: String,
      default: '100%',
    },
  },
  setup(props) {
    const chartOptions = computed(() => {
      return getChartOption(props.data, props.xAxisData, props.yAxisData);
    });
    const chartRef = ref<any>(null);
    const wrapperRef = ref<any>(null);

    const { addTooltip } = useLabelTooltip(wrapperRef, {
      boundary: {
        left: chartOptions.value.grid.left,
        bottom: chartOptions.value.grid.bottom,
      },
      exclude: [chartOptions.value.xAxis.name],
    });

    nextTick(() => {
      const chartInstance = chartRef.value?.chart;
      const zr = chartInstance?.getZr();
      zr.on('mousemove', (e) => {
        if (typeof zr.setCursorStyle === 'function') {
          zr.setCursorStyle('default');
        }
        const text = e.topTarget?.parent?.style?.text;
        addTooltip(e.offsetX, e.offsetY, text);
      });
    });

    useChartResize(chartRef);

    return {
      chartOptions,
      chartRef,
      wrapperRef,
    };
  },
  render() {
    const { width, height, chartOptions } = this;
    return (
      <div
        ref="wrapperRef"
        class={styles.container}
        style={{
          width,
          height,
        }}
      >
        <VChart
          ref="chartRef"
          option={chartOptions}
          on={{
            click: (record) => {
              this.$emit('click', record);
            },
          }}
        />
      </div>
    );
  },
});

export default HeatmapChart;
