@import 'ant-design-vue/dist/antd.less';
@import './theme.less';
@import './share/common.less';
@import './font.less';

body {
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  color: @text-color;
  background: #f7f7f7;
  font: @rover-font-base;
  scrollbar-width: thin;
  overflow: overlay;

  &::-webkit-scrollbar {
    width: 8px;
    height: 8px;
  }
}

// 全局滚动条宽度4px，#e3e3e3
::-webkit-scrollbar {
  width: 4px;
  height: 6px;
}

::-webkit-scrollbar-track {
  background-color: #f8f8f8;
}

::-webkit-scrollbar-thumb {
  background-color: #e3e3e3;
  border-radius: 6px;
  background-clip: padding-box;

  &:hover {
    background-color: rgba(50, 50, 50, .5)
  }
}

.router-tab__iframe {
  height: calc(100vh - 80px);
}

a {
  color: @qcc-color-blue-500;

  &:hover {
    color: @qcc-color-blue-600;
  }
}

#app {
  // min-width: 100vw; windows 滚动条有 bug
  // min-width: 1440px;
  min-width: 100%;
  min-height: 100vh;
}

.text-gray {
  color: #999;
}

.text-right {
  text-align: right;
}

.page-loading {
  display: flex;
  height: calc(100vh - 168px);
  align-items: center;
  justify-content: center;
}

.q-static-table {
  width: 100%;
  margin-bottom: 10px;
  color: #333;
  font-size: 14px;
  line-height: 22px;
  border-collapse: collapse;

  colgroup {
    col {
      &:nth-child(odd) {
        width: 14.7%;
      }
    }
  }

  td {
    padding: 10px 15px;
    border: 1px solid #e4eef6;

    &:nth-child(odd) {
      color: #666;
      background: #f2f9fc;
    }
  }
}

// Simplebar theme
.simplebar {
  &-track&-vertical {
    width: 8px;
  }

  &-scrollbar {
    &::before {
      background: #b5b5b5;
    }
  }
}

// table 拖拽
.beforLine {
  td {
    border-top: 1.1px dashed #1890ff !important;
  }
}

.afterLine {
  td {
    border-bottom: 1px dashed #1890ff !important;
  }
}

.flex-between {
  flex: 1;
  display: flex;
  align-items: center;
  justify-content: space-between;
}

// 面包屑
.breadcrumb {
  position: fixed;
  padding: 15px 14px;
  top: 52px;
  height: 50px;
  background-color: #f7f7f7;
  left: 189px;
  right: 0;
  z-index: 11;

  svg {
    margin-right: 6px;
  }

  .firstItem {
    color: @qcc-color-blue-500;

    &:hover {
      color: @qcc-color-blue-600;
    }
  }
}

.sticky-breadcrumb {
  position: sticky;
  top: 0;
  left: 0;
  height: 50px;
  width: 100%;
  background-color: #f7f7f7;
  z-index: 11;
  margin-top: -10px;
  display: flex;
  align-items: center;

  a {
    color: @qcc-color-blue-500;
  }

  .ant-breadcrumb-separator {
    color: @qcc-color-black-300;
    margin: 0 5px;
  }

  &.ant-breadcrumb svg,
  .ant-breadcrumb svg {
    margin-right: 6px;
  }

  .ant-breadcrumb > span:last-child,
  & > span:last-child {
    color: @qcc-color-black-600;
  }

  .ant-breadcrumb > span:first-child,
  & > span:first-child {
    color: @qcc-color-blue-500;

    &:hover {
      color: @qcc-color-blue-600;
    }
  }
}

.drawer-title {
  display: flex;
  align-items: center;
  justify-content: space-between;

  .icon-close {
    color: #bbb;
  }
}

.ant-drawer-close:hover {
  color: #128bed;
}

[tip] {
  cursor: pointer;
  position: relative;
}

[tip]::after {
  content: attr(tip);
  visibility: hidden;
  position: absolute;
  top: -5px;
  transition: all 0.3s;
  width: 200px;
  background: rgba(0, 0, 0, 0.8);
  box-shadow: 0 2px 4px 0 rgba(0, 0, 0, 0.2);
  padding: 5px 10px;
  z-index: 9999;
  left: 50%;
  transform: translate(-50%, -100%);
  word-break: break-all;
  border-radius: 2px;
  font-size: 14px;
  color: #fff;
}

[tip]:hover {
  overflow: visible;
}

[tip]:hover::after {
  visibility: visible;
}

.modal-table {
  // 解决弹窗中的表格在有滚动条时，表格右侧和其他元素不对齐的问题
  .ant-table-scroll {
    margin-right: -4px;
  }
  // 兼容firefox样式
  @-moz-document url-prefix() {
    .ant-table-scroll {
      margin-right: 0;
    }
    // firefox的滚动条不占位，表头宽度需去掉4px的滚动条占位，否则表格头和内容不对齐
    .ant-table-header  {
      width: 100% !important;
    }
  }
}
