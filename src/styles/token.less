@import './vars.less';

/**
 * Framework design token override
 * https://github.com/vueComponent/ant-design-vue/blob/next/components/style/themes/default.less
 */

// Rover defined
@rover-font-base-size: 14px;
@rover-font-base-weight: 400;
@rover-font-base-height: 20px;
@rover-font-family: Helvetica, Tahoma, Arial, 'PingFang SC', 'Hiragino Sans GB', 'Heiti SC', 'Microsoft YaHei',
  sans-serif;

@rover-font-base: 14px / @rover-font-base-height @rover-font-family;

@font-family: @rover-font-family;
// Color
@single-icon-color: #bbb;
@text-color: #333;
@info-text-color: #999;
@text-color-secondary: rgba(0, 0, 0, 0.45);
@primary-color: #128bed;
@link-color: #128bed;
@success-color: #52c41a;
@warning-color: #f9ad14;
@info-color: @primary-color;
@danger-color: #f04040;
@highlight-color: #f04040;
@font-size-base: @rover-font-base-size;
@heading-color: #333;
@disabled-color: rgba(0, 0, 0, 0.25);
@border-radius-base: 2px;
@qcc-border-radius-base: 2px;
@qcc-border-radius-middle: 4px;
@box-shadow-base: 0 2px 8px rgba(0, 0, 0, 0.15);
// @border-color-base: #eee;
@border-color-base: #d8d8d8;
@border-color-split: #eee;
@padding-lg: 20px;
@padding-md: 16px;
@padding-sm: 12px;
@padding-xs: 8px;

// Button
@btn-font-size-sm: @font-size-base;

@btn-height-base: 32px;
@btn-height-lg: 40px;
@btn-height-sm: 22px;

// Layout
@layout-body-background: none;

// Card
@card-padding-base: 20px;

// Tabs
@tabs-horizontal-padding: 12px 0;
@tabs-horizontal-padding-lg: 18px 0;
@tabs-horizontal-padding-sm: 8px 0;
@tabs-highlight-color: @heading-color;
@tabs-hover-color: @heading-color;

// Modal
@modal-body-padding: 12px 16px;
@modal-footer-padding-vertical: 12px;
@modal-footer-padding-horizontal: 16px;

// Drawer
@drawer-header-padding: 18px 20px;
@drawer-body-padding: 20px;

// Menu
@item-active-bg: #f3f9fd;
@item-hover-bg: #f4f9fc;
@menu-item-color: #666;
@menu-highlight-color: @primary-color;
@menu-item-active-bg: @item-active-bg;
@menu-item-active-border-width: 2px;
@menu-item-boundary-margin: 4px;
@menu-collapsed-width: 62px;

// Alert
@alert-success-border-color: transparent;
@alert-success-bg-color: #edf8e8;
@alert-success-icon-color: @success-color;
@alert-info-border-color: transparent;
@alert-info-bg-color: #f3f9fd;
@alert-info-icon-color: @info-color;
@alert-warning-border-color: transparent;
@alert-error-border-color: transparent;

// Table
// --
@rover-table-border-color: #e4eef6;
@rover-table-header-bg: #f2f8fe;
@table-th-padding: @qcc-table-base-cell-gap;
@table-td-padding: @qcc-table-base-cell-gap;
@table-font-size: @rover-font-base-size;

// @table-bg: #f2f9fc;
@table-header-bg: @rover-table-header-bg;
@table-header-sort-bg: @table-header-bg;
@table-body-sort-bg: none;
@table-row-hover-bg: #f2f9ff;
// @table-selected-row-color: inherit;
// @table-selected-row-bg: @primary-1;
// @table-body-selected-sort-bg: @table-selected-row-bg;
// @table-selected-row-hover-bg: darken(@table-selected-row-bg, 2%);
// @table-expanded-row-bg: #fbfbfb;
@table-padding-vertical: 14px;
// @table-padding-horizontal: 16px;
// @table-padding-vertical-md: (@table-padding-vertical * 3 / 4);
// @table-padding-horizontal-md: (@table-padding-horizontal / 2);
@table-padding-vertical-sm: (@table-padding-vertical / 2);
@table-padding-horizontal-sm: 12px;
@table-border-color: @rover-table-border-color;
@table-border-radius-base: 0;
// @table-footer-bg: @background-color-light;
// @table-footer-color: @heading-color;
@table-header-bg-sm: @table-header-bg;
// @table-font-size: @font-size-base;
// @table-font-size-md: @table-font-size;
@table-font-size-sm: @table-font-size;
@table-header-cell-split-color: @rover-table-border-color;
// Sorter
// Legacy: `table-header-sort-active-bg` is used for hover not real active
// @table-header-sort-active-bg: @rover-table-header-bg;
// @table-header-sort-active-bg: #c00;
// Filter
// @table-header-filter-active-bg: rgba(0, 0, 0, 0.04);
// @table-filter-btns-bg: inherit;
// @table-filter-dropdown-bg: @component-background;
// @table-expand-icon-bg: @component-background;
// @table-selection-column-width: 32px;
// // Sticky
// @table-sticky-scroll-bar-bg: fade(#000, 35%);
// @table-sticky-scroll-bar-radius: 4px;

// Pagination
// ---
// @pagination-item-bg: @component-background;
// @pagination-item-size: @height-base;
@pagination-item-size: 32px;
@pagination-item-size-sm: 22px;
// @pagination-font-family: @font-family;
// @pagination-font-weight-active: 500;
@pagination-font-weight-active: bold;
// @pagination-item-bg-active: @component-background;
@pagination-item-bg-active: @qcc-color-blue-500;
// @pagination-item-link-bg: @component-background;
// @pagination-item-disabled-color-active: @white;
// @pagination-item-disabled-bg-active: darken(@disabled-bg, 10%);
// @pagination-item-input-bg: @component-background;
// @pagination-mini-options-size-changer-top: 0px;

// Tooltip
// ---
// Tooltip max width
@tooltip-max-width: 300px;
// Tooltip text color
// @tooltip-color: @text-color;
// Tooltip background color
@tooltip-bg: rgba(0, 0, 0, 0.8);
// Tooltip arrow width
@tooltip-arrow-width: 0;
// Tooltip distance with trigger
@tooltip-distance: @tooltip-arrow-width - 1px + 4px;
// Tooltip arrow color
// @tooltip-arrow-color: none;

// Checkbox
@checkbox-size: 14px;
@checkbox-color: @primary-color;

// Radio
@radio-size: 14px;
@radio-dot-color: @primary-color;

// Wave
@wave-animation-width: 0;

// KYC -----------------------
// Colors
// ---------------------------------------------------------------
@qcc-color-white: #fff;

@qcc-color-black-800: #000000;
@qcc-color-black-600: #333333;
@qcc-color-black-500: #666666; // 前景色
@qcc-color-black-400: #808080;
@qcc-color-black-300: #999999;
@qcc-color-black-200: #bbbbbb;

@qcc-color-gray-700: #d8d8d8;
@qcc-color-gray-600: #e3e3e3;
@qcc-color-gray-500: #eeeeee;
@qcc-color-gray-400: #f5f5f5;
@qcc-color-gray-300: #f3f3f3; // 背景色
@qcc-color-gray-200: #f8f8f8;
@qcc-color-gray-100: #fbfbfb;

@qcc-color-blue-700: #336cb4;
@qcc-color-blue-600: #0069bf; // Hover
@qcc-color-blue-500: #128bed; // 前景色
@qcc-color-blue-400: #88c5f6;
@qcc-color-blue-300: #e5f2fd; // 背景色
@qcc-color-blue-200: #f2f8fe;
@qcc-color-blue-100: #fafcff;

@qcc-color-red-600: #f04040; // 标找找
@qcc-color-red-500: #f04040;
@qcc-color-red-300: #ffecec;

@qcc-color-green-500: #00ad65; // 前景色
@qcc-color-green-300: #e3f6ee; // 背景色

@qcc-color-yellow-500: #ffaa00;
@qcc-color-yellow-300: #fff3db;

@qcc-color-orange-500: #ff722d;
@qcc-color-orange-300: #ffeee6;

// 灰色
// @qcc-color-black-500: #666666; // 前景色
// @qcc-color-gray-300: #f3f3f3; // 背景色

// 蓝紫色
@qcc-color-violet-500: #845fff;
@qcc-color-violet-300: #f0ebff;

// 钴蓝
@qcc-color-zaffer-500: #6171ff;
@qcc-color-zaffer-300: #edeeff;

// 科技蓝
@qcc-color-scifi-500: #367dff;
@qcc-color-scifi-300: #e9f1ff;

// 金色
@qcc-color-cyan-500: #00a3cc;
@qcc-color-cyan-300: #dff3f8;

// 金色
@qcc-color-gold-500: #bb833d;
@qcc-color-gold-300: #f6f0e7;

// Text size
// ---------------------------------------------------------------
@qcc-text-xs: 12px;
@qcc-text-sm: 13px;
@qcc-text-md: 14px;
@qcc-text-lg: 16px;
@qcc-text-xl: 18px;
@qcc-text-2xl: 20px;
@qcc-text-3xl: 24px;

// Font weight
// ---------------------------------------------------------------
@qcc-font-light: 300;
@qcc-font-normal: 400;
@qcc-font-medium: 500;
@qcc-font-bold: 700;

// Line height
// ---------------------------------------------------------------
@qcc-leading-1: 18px;
@qcc-leading-2: 20px;
@qcc-leading-3: 22px;
@qcc-leading-4: 24px;
@qcc-leading-5: 26px;
@qcc-leading-6: 28px;
@qcc-leading-7: 30px;

// Mixins
// ---------------------------------------------------------------
@qcc-table-base-font-size: @qcc-text-sm;
@qcc-table-base-cell-gap: 9px 10px;
@qcc-table-header-bg: @qcc-color-blue-200;
@qcc-table-border-color: #e4eef6;
@qcc-table-row-hover-bg: #f2f8fe;

@keyframes blink {
  0%,
  100% {
    background-color: #000;
    color: #fff;
  }

  50% {
    background-color: #fff;
    color: #000;
  }
}

.faker-blink {
  display: inline-block;
  line-height: inherit;
  width: 1px;
  animation: blink 1s infinite steps(1, start);
  vertical-align: middle;
  height: 15px;
}

.span-link {
  cursor: pointer;

  &:hover {
    color: #128bed;
  }
}

.drag-handle {
  font-size: 14px;
  cursor: pointer;
  color: #bbb;

  &:hover {
    color: #128bed;
  }
}

.drag-handle-disabled {
  color: #e3e3e3 !important;
  cursor: default !important;
}

.q-filter-wrap--filters {
  max-width: calc(100% - 105px);
  gap: 5px 0;
}

.self-customed {
  .ant-tooltip-inner {
    color: #333;
    background: white;
  }
}

.trends-content{
  .ant-btn-link,
  a {
    color: #333;
  }
}

.wrong-edit-btn {
  display: none;
}

tr:hover{
  .wrong-edit-btn {
    display: inline-block;
  }

  .trends-content{
    .ant-btn-link,
    a {
      color: @link-color;

      &:hover {
        color: #0069bf;
      }
    }
  }
}

.emphasis em {
  color: #F04040;
}

.table-sorter-menu {
  padding-top: 0;

  .ant-popover-content {
    max-width: 400px;
    border-radius: 2px;
    transform: translateY(-6px);

    .ant-popover-arrow {
      display: block;
      top: 28px;
      border-right-color: rgba(0, 0, 0, 0.8);
      border-bottom-color: rgba(0, 0, 0, 0.8);
      z-index: -1;
    }

    .ant-popover-inner {
      background-color: rgba(0, 0, 0, 0.8);

      .ant-popover-inner-content {
        padding: 6px 8px;
      }
    }
  }
}
