import Vue, { unref } from 'vue';
import VueRouter, { RouteConfig } from 'vue-router';
import { message } from 'ant-design-vue';

import { hasPermission } from '@/shared/composables/use-permission';
import { useUserStore } from '@/shared/composables/use-user-store';
import { useStore } from '@/store';
import { embedRoutes } from '@/apps/embed/routes/embed.routes';

Vue.use(VueRouter);

const routes: RouteConfig[] = embedRoutes();

export const embedRouter = new VueRouter({
  mode: 'history',
  base: '/embed',
  routes,
  linkActiveClass: 'active',
  linkExactActiveClass: 'exact-active',
  scrollBehavior() {
    return { x: 0, y: 0 };
  },
});

// 鉴权
embedRouter.beforeEach(async (to, from, next) => {
  const { permissions, profile } = useUserStore();
  const { meta = {} } = to;
  const { permission } = meta;

  const store = useStore();
  // 判断是否有权限，没有就获取
  if (!permissions?.value?.length) {
    try {
      await store.dispatch('user/getProfile');
    } catch (error) {
      console.error(error);
    }
  }
  const bundleError = unref(profile)?.bundleError;
  if (bundleError) {
    message.warn({
      key: bundleError?.code,
      content: bundleError?.error,
    });
  }

  // 设置权限控制并且有权限，进入
  if (permission && !hasPermission(permission) && !bundleError) {
    // TODO: 跳转到错误页面时，带上错误消息
    // const code = Array.isArray(permission) ? permission[0] : permission;
    // const content = getNoPermissionLabelByCode(code);
    // // 登录才提示，没登录不提示
    // if (unref(profile)?.userId) {
    //   message.warn({ key: code, content });
    // }
    window.location.replace('/error/403');
  }
  next();
});

// 发版过程中Chunk文件加载失败刷新页面
embedRouter.onError((error) => {
  const { message, name } = error;
  const pattern = /^Failed to fetch dynamically imported module/;
  if (pattern.test(message) || ['ChunkLoadError'].includes(name)) {
    window.location.reload();
  }
});
