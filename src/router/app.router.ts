import Vue, { unref } from 'vue';
import VueRouter from 'vue-router';
import { message } from 'ant-design-vue';

import { getNoPermissionLabelByCode, hasPermission } from '@/shared/composables/use-permission';
import { useUserStore } from '@/shared/composables/use-user-store';
import { useStore } from '@/store';

import { routes } from './routes/app.routes';

Vue.use(VueRouter);

export const appRouter = new VueRouter({
  mode: 'history',
  base: '/app',
  routes,
  linkActiveClass: 'active',
  linkExactActiveClass: 'exact-active',
  scrollBehavior() {
    return { x: 0, y: 0 };
  },
});

// 鉴权
// TODO: 暂时停用路由鉴权
appRouter.beforeEach(async (to, from, next) => {
  const { permissions, profile } = useUserStore();
  const WHITE_LAYOUT_LIST = ['default'];
  const { meta = {}, name: toName } = to;
  const { layout, permission } = meta;

  const isExternalAuthPage = to.path.startsWith('/external/auth/'); // 外部鉴权页面

  // 错误提示页面以及外部链接直接跳过，不需要权限判断
  if (isExternalAuthPage) {
    next();
    return;
  }

  // 是否是落地页或者外部链接
  const isLandingPage = toName === 'index' || WHITE_LAYOUT_LIST.includes(layout);

  const store = useStore();
  // 判断是否有权限，没有就获取
  if (!unref(permissions).length) {
    try {
      await store.dispatch('user/getProfile');
    } catch (error) {
      console.error(error);
    }
  }
  const bundleError = unref(profile)?.bundleError;
  if (bundleError && toName !== 'switch-org' && (isLandingPage || from.name === 'index')) {
    message.warn({
      key: bundleError?.code,
      content: bundleError?.error,
    });
  }

  // bundleError说明套餐有错误，需要处理
  // 针对落地页点击按钮有问题的时候，直接刷新页面即可
  if (!isLandingPage && bundleError) {
    if (from.name === 'index-page') {
      window.location.reload();
    } else {
      next({ name: 'index-page' });
    }
    return;
  }

  // 设置权限控制并且有权限，进入
  if (permission && !hasPermission(permission) && !bundleError) {
    // TODO: 如果是嵌入页面，无权限访问应当跳转到 error/403
    // window.location.replace('/error/403');
    next({ name: 'index-page', replace: true });
    const code = Array.isArray(permission) ? permission[0] : permission;
    const content = getNoPermissionLabelByCode(code);
    // 登录才提示，没登录不提示
    if (unref(profile)?.userId) {
      message.warn({ key: code, content });
    }
    return;
  }
  next();
});

appRouter.afterEach(() => {
  const layoutEl = document.getElementById('workbench-layout-main');
  if (layoutEl) {
    layoutEl.scrollTo(0, 0);
  }
});

// 发版过程中Chunk文件加载失败刷新页面
appRouter.onError((error) => {
  const { message, name } = error;
  const pattern = /^Failed to fetch dynamically imported module/;
  if (pattern.test(message) || ['ChunkLoadError'].includes(name)) {
    window.location.reload();
  }
});
