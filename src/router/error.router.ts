import Vue from 'vue';
import VueRouter, { RouteConfig } from 'vue-router';

import { errorRoutes } from '@/apps/error/routes/error.routes';

Vue.use(VueRouter);

const routes: RouteConfig[] = errorRoutes();

export const errorRouter = new VueRouter({
  mode: 'history',
  base: '/error',
  routes,
  linkActiveClass: 'active',
  linkExactActiveClass: 'exact-active',
  scrollBehavior() {
    return { x: 0, y: 0 };
  },
});

// 发版过程中Chunk文件加载失败刷新页面
errorRouter.onError((error) => {
  const { message, name } = error;
  const pattern = /^Failed to fetch dynamically imported module/;
  if (pattern.test(message) || ['ChunkLoadError'].includes(name)) {
    window.location.reload();
  }
});
