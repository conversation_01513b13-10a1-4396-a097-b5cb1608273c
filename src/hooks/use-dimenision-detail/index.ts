import { DETAIL_TYPES_MAP, getDetailByType } from '@/config/risk-detail.config';

export const useDimensionDetail = (vm, props) => {
  const showDetail = (key: string, item: Record<string, any>) => {
    let ntype: any = '';
    if (item.CaseReasonType === '工业品') {
      ntype = '2';
    } else {
      ntype = '1';
    }
    const params: any = {
      keyNo: props.meta.keyNo,
      id: item.id || item.Id || item.No,
      type: ntype,
      dimensionKey: item.dimensionKey,
      Details: item.Details,
    };
    const dialogProps: Record<string, any> = {};
    switch (key) {
      case 'ViolationProcessings':
        Object.assign(params, item);
        break;
      case 'TaxationOffences':
      case 'FreezeEquity':
        params.id = item.RiskId;
        break;
      case 'EquityPledge':
        params.pledgeId = item.No;
        break;
      case 'CapitalReduction':
        params.type = 'deceaseCapiNotice';
        break;
      case 'CancellationOfFiling':
        dialogProps.Detail = item.Detail;
        break;
      case 'Bankruptcy':
        dialogProps.applicant = item.NameAndKeyNo?.filter((v) => v.Org === -1) || [];
        break;
      case 'PledgeMerger':
        dialogProps.sourcefrom = item.sourcefrom;
        dialogProps.Detail = item;
        break;
      case 'AssetInvestigationAndFreezing':
        Object.assign(params, item);
        break;
      case 'ActuralControllerInformation':
        Object.assign(params, {
          keyNo: item.KeyNo,
          name: item.Name,
          toKeyNo: props.meta.keyNo,
        });
        break;
      default:
    }
    vm.$modal.showDimension(DETAIL_TYPES_MAP[key], params, dialogProps);
  };

  const goDetail = (url: string) => {
    if (!url) {
      return;
    }
    window.open(url);
  };
  const showInfo = (key: string, item: Record<string, any>) => {
    let type = '';
    if (key === 'ProductQualityProblem5') {
      switch (item.Type) {
        case '21':
          type = 'drc';
          break;
        default:
          break;
      }
    }
    vm.$modal.showDimension(DETAIL_TYPES_MAP[key] || type, item);
  };

  const gotoDetail = (key: string, item: Record<string, any>) => {
    const url = getDetailByType(key, item);
    window.open(url);
  };

  return {
    showDetail,
    goDetail,
    showInfo,
    gotoDetail,
  };
};
