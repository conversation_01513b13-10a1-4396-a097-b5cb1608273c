{"name": "qcc-insights-web", "version": "1.0.0", "private": true, "type": "module", "scripts": {"dev": "vite", "build": "run-p type-check build-only", "build-only": "vite build", "type-check": "vue-tsc --noEmit -p tsconfig.app.json --composite false", "test:unit": "vitest run --coverage.enabled --coverage.clean", "test:ui": "vitest --ui --coverage.enabled --coverage.clean", "test:cov": "vitest run --coverage.enabled --coverage.clean", "test:report": "vite preview --outDir test-report/", "test:changed": "vitest watch", "lint:js": "eslint ./src --ext .vue,.js,.jsx,.cjs,.mjs,.ts,.tsx,.cts,.mts --fix --ignore-path .gitignore", "lint:css": "stylelint \"src/**/*.{css,less}\" --fix --formatter", "lint": "run-p lint:*", "check:types": "vue-tsc --noEmit", "sonar": "sonar-scanner", "postinstall": "patch-package", "prepare": "husky"}, "dependencies": {"@sentry/vue": "^7.74.1", "@vueuse/core": "^11.3.0", "ant-design-vue": "^1.7.8", "axios": "^1.9.0", "big.js": "^6.1.1", "canvg": "2.0.0", "core-js": "^3.8.3", "crypto-js": "^4.2.0", "cytoscape": "^3.23.0", "cytoscape-automove": "^1.10.3", "cytoscape-popper": "^2.0.0", "d3": "5.12.0", "d3v7": "npm:d3@^7.1.1", "dexie": "^3.2.4", "diff": "^5.1.0", "echarts": "^5.6.0", "element-resize-detector": "^1.2.3", "eventemitter3": "^5.0.1", "file-saver": "^2.0.5", "html2canvas": "^1.4.1", "js-cookie": "^3.0.1", "loadjs": "^4.2.0", "lodash": "^4.17.21", "moment": "^2.30.1", "normalize.css": "^8.0.1", "querystring": "^0.2.1", "save-svg-as-png": "1.4.14", "socket.io-client": "^4.7.5", "ua-parser-js": "^1.0.33", "url-template": "^2.0.8", "uuid": "3.3.3", "validator": "^13.7.0", "vue": "^2.7.16", "vue-echarts": "^7.0.3", "vue-i18n": "^8.0.0", "vue-router": "^3.6.5", "vue-scrollto": "^2.20.0", "vue-typed-mixins": "^0.2.0", "vuedraggable": "^2.24.3", "vuex": "^3.6.2"}, "devDependencies": {"@babel/core": "^7.16.0", "@babel/plugin-transform-optional-chaining": "^7.27.1", "@commitlint/cli": "^17.7.0", "@qcc-ui/commitlint-config": "^1.0.0", "@qcc-ui/stylelint-config": "^0.1.0", "@rushstack/eslint-patch": "^1.10.4", "@types/d3": "^5", "@types/d3v7": "npm:@types/d3@^7.1.0", "@types/jquery": "^3.5.8", "@types/js-cookie": "^3.0.1", "@types/lodash": "^4.14.176", "@types/node": "^18.13.0", "@types/validator": "^13.7.0", "@unocss/preset-wind4": "^66.1.3", "@vitejs/plugin-vue2": "^1.1.2", "@vitejs/plugin-vue2-jsx": "^1.0.2", "@vitest/coverage-v8": "^3.1.4", "@vitest/ui": "^3.1.4", "@vue/babel-helper-vue-jsx-merge-props": "^1.4.0", "@vue/eslint-config-prettier": "^8.0.0", "@vue/eslint-config-typescript": "^12.0.0", "@vue/test-utils": "^1.3.6", "code-inspector-plugin": "^0.20.12", "connect-history-api-fallback": "^2.0.0", "eslint": "^8.49.0", "eslint-import-resolver-typescript": "^3.6.3", "eslint-plugin-import": "^2.31.0", "eslint-plugin-prettier": "^4.0.0", "eslint-plugin-vue": "^9.17.0", "happy-dom": "^17.4.7", "husky": "^9.1.7", "jest-serializer-vue": "^3.1.0", "less": "^4.0.0", "lint-staged": "^15.5.1", "npm-run-all2": "^6.1.1", "patch-package": "^8.0.0", "postcss-html": "^1.5.0", "postcss-less": "^6.0.0", "prettier": "^3.3.3", "sonar-scanner": "^3.1.0", "stylelint": "^15.5.0", "stylelint-config-html": "^1.1.0", "terser": "^5.14.2", "typescript": "~5.3.0", "unocss": "^66.1.3", "vite": "5.4.8", "vitest": "^3.1.4", "vitest-sonar-reporter": "^2.0.1", "vue-template-compiler": "^2.7.16", "vue-tsc": "^2.1.6"}, "lint-staged": {"*.{css,less}": "stylelint --fix", "*.{vue,js,jsx,cjs,mjs,ts,tsx,cts,mts}": "eslint --fix --ignore-path .gitignore"}, "resolutions": {"vite": "5.4.8"}, "optionalDependencies": {"@rollup/rollup-darwin-x64": "4.39.0", "@rollup/rollup-linux-x64-gnu": "4.39.0", "@rollup/rollup-win32-x64-msvc": "4.39.0"}, "packageManager": "yarn@1.22.22+sha512.a6b2f7906b721bba3d67d4aff083df04dad64c399707841b7acf00f6b133b7ac24255f2652fa22ae3534329dc6180534e98d17432037ff6fd140556e2bb3137e"}