import Vue from 'vue';
import { Modal } from 'ant-design-vue';
import { vi, expect } from 'vitest';
import { config } from '@vue/test-utils';
import vueSnapshotSerializer from 'jest-serializer-vue';
import _ from 'lodash';

expect.addSnapshotSerializer(vueSnapshotSerializer);

const setup = () => {
  // -------------------------------------
  // Vue setup
  // -------------------------------------
  Vue.config.silent = true;
  Vue.config.productionTip = false;
  Vue.config.devtools = false;
  Vue.use(Modal); // AntPortal directive
  // global.h = Vue.h; // 避免抛出找不到 `h` 的异常

  // -------------------------------------
  // Jest mock setup
  // -------------------------------------
  vi.mock('vue-router/composables', () => {
    const promisifyFn = () => vi.fn().mockResolvedValue(undefined);
    const mockRoute = {
      url: '',
      path: '',
      hash: '',
      name: '',
      query: {},
      params: {},
      fullPath: '',
      matched: [],
      meta: {},
    };
    const mockRouter = {
      options: {
        routes: [],
      },
      push: promisifyFn(),
      replace: promisifyFn(),
      back: promisifyFn(),
      go: promisifyFn(),
      forward: promisifyFn(),
      resolve: promisifyFn(),
      catch: vi.fn(),
      currentRoute: mockRoute,
    };
    return {
      useRouter: () => {
        return mockRouter;
      },
      useRoute: () => {
        return mockRoute;
      },
    };
  });
  vi.mock('@/config/tracking-events', () => {
    return {
      createTrackEvent: vi.fn(() => null),
      useTrack: vi.fn(() => () => null),
    };
  });
  vi.mock('@/shared/composables/use-translate', () => ({
    useTranslateZeiss: () => ({
      t: (key) => `TZ: ${key}`,
    }),
    getTranslateZeissFilterGroups: (v) => v,
    getTranslateZeissTableColumn: (v) => v,
  }));
  // -------------------------------------
  // Component mock
  // -------------------------------------
  vi.mock('@/components/global/q-icon', () => ({
    default: {
      functional: true,
      render(h, ctx) {
        return h(
          'q-icon-stub',
          {
            attrs: ctx.props,
            ref: ctx.data.ref,
            on: ctx.data.on,
          },
          ctx.children
        );
      },
    },
  }));
  vi.mock('@/components/full-watermark', () => {
    return {
      default: {
        functional: true,
        render: (h, ctx) => {
          return h(
            'full-watermark',
            {
              attrs: ctx.props,
            },
            ctx.children
          );
        },
      },
    };
  });
  vi.mock('@/shared/layouts/heroic', () => {
    return {
      default: {
        functional: true,
        render: (h, ctx) => {
          return h(
            'hero-layout',
            {
              attrs: ctx.props,
            },
            ctx.children
          );
        },
      },
    };
  });

  // -------------------------------------
  // Vue test util setup
  // -------------------------------------
  config.showDeprecationWarnings = false;
  config.stubs = {
    'router-link': true,
    'router-view': true,
    QIcon: true,
    'q-icon': true,
  };
  config.mocks = {
    $track: _.noop,
    $message: {
      info: _.noop,
      success: _.noop,
      error: _.noop,
      warn: _.noop,
      warning: _.noop,
      loading: _.noop,
      destroy: _.noop,
    },
  };
};

setup();
