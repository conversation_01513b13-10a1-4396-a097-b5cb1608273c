include:
  - project: 'kezhaozhao/tools/qcc-deployment'
    ref: static
    file: '/gitlab/template.frontend.v3.yml'

variables:
  DEPENDENCIES_IMAGE: 'harbor-in.greatld.com/kezhaozhao/node:22.16.0-slim'
  SENTRY_DSN: 'https://<EMAIL>/36'
  OSS_PROJECT_FOLDER: rover/insights/site/$CI_PIPELINE_ID
  NS_RELEASE: 'release'
  NS_PROD: 'rover'
  CLUSTER: 'rover'
  SONAR_TOKEN: 'sqp_8616a5c23b5ddf0ea6f8820806ad1a0c6ba363bc'
  MOBILE_NUMBERS: '18625000947'
  SERVICE_IMAGE_BASE: $HARBOR_REPO/$CI_PROJECT_NAME:base-1.2.0

.tags_job:
  tags:
    - frontend_rover

oss:
  extends:
    - .oss_rover
  only:
    - master
