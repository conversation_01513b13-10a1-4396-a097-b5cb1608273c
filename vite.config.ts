import { fileURLToPath, URL } from 'node:url';
import { AliasOptions, defineConfig, Plugin, type PluginOption } from 'vite';
import vue from '@vitejs/plugin-vue2';
import jsx from '@vitejs/plugin-vue2-jsx';
import uno from 'unocss/vite';
import history from 'connect-history-api-fallback';
import { resolve } from 'node:path';
import { codeInspectorPlugin as codeInspector } from 'code-inspector-plugin';
import { version as APP_VERSION } from './package.json';

/**
 * 扩展默认 VData 指令
 */
const vDataDirectiveModule = () => {
  const preTransformNode = (el: any) => {
    const data = el?.attrsList?.find(({ name }: { name: string }) => name === 'v-data');
    if (data) {
      el.attrsList = el.attrsList
        .filter(({ name }: { name: string }) => name !== 'v-data')
        .concat([
          {
            ...data,
            name: 'v-bind',
            value: `${data.value}.props`,
          },
          {
            ...data,
            name: 'v-on',
            value: `${data.value}.on`,
          },
          {
            ...data,
            name: ':ref',
            value: `${data.value}.ref`,
          },
        ]);
      el.attrsMap = {
        ...el.attrsMap,
        'v-bind': `${data.value}.props`,
        'v-on': `${data.value}.on`,
        ':ref': `${data.value}.ref`,
      };

      delete el.attrsMap['v-data'];
    }
  };
  return {
    preTransformNode,
  };
};

/**
 * Vite server plugin
 * 方便开发时调试MPA应用
 */
const pathRewrite = (): Plugin => {
  return {
    name: 'path-rewrite',
    configureServer: (server) => {
      server.middlewares.use(
        history({
          verbose: false,
          htmlAcceptHeaders: ['text/html', 'application/xhtml+xml'],
          index: '/index.html',
          rewrites: [
            { from: /\/app/, to: '/index.html' },
            { from: /\/error/, to: '/error.html' },
            { from: /\/embed/, to: '/embed.html' },
            // { from: /\/external/, to: '/external.html' },
          ],
        })
      );
    },
  };
};

const ALIAS: AliasOptions = {
  '@': fileURLToPath(new URL('./src', import.meta.url)),
  d3: fileURLToPath(new URL('./node_modules/d3', import.meta.url)),
  d3v7: fileURLToPath(new URL('./node_modules/d3v7', import.meta.url)),
};

export default defineConfig(({ mode }) => {
  const { SENTRY_DSN, SENTRY_RELEASE, ENABLE_OSS, OSS_URL, OSS_PROJECT_FOLDER, SENTRY_ENVIRONMENT = 'test' } = process.env;

  const isDev = mode === 'development';
  const APP_RUNTIME_ENV = isDev ? 'test' : SENTRY_ENVIRONMENT;

  const localPublicPath = '/';
  /**
   * 根据环境获取PublicPath
   */
  const getPublicPath = (currentMode: string, defaultPath = '/') => {
    if (currentMode === 'production' && ENABLE_OSS === 'true' && OSS_URL && OSS_PROJECT_FOLDER) {
      const publicPath = `${OSS_URL.replace(/(^https?:\/\/)/, '//')}/${OSS_PROJECT_FOLDER}`;
      return publicPath;
    }
    return defaultPath;
  };

  /**
   * 根据环境选择插件
   */
  const getPlugins = (currentMode: string) => {
    const plugins: PluginOption = [
      vue({
        template: {
          compilerOptions: {
            modules: [vDataDirectiveModule()],
          },
        },
      }),
      jsx(),
      uno(),
    ];
    if (currentMode === 'development') {
      plugins.push(pathRewrite());
      plugins.push(
        codeInspector({
          bundler: 'vite',
          editor: 'code', // 编辑器的类型
          hideDomPathAttr: true, // 是否隐藏dom上的data-insp-path
          exclude: ['/src/components/global/'],
        } as any)
      );
    }
    return plugins;
  };

  return {
    base: getPublicPath(mode, localPublicPath),
    build: {
      target: 'es2015',
      rollupOptions: {
        input: {
          index: resolve(__dirname, 'index.html'),
          embed: resolve(__dirname, 'embed.html'),
          error: resolve(__dirname, 'error.html'),
          // external: resolve(__dirname, 'external.html'),
        },
        output: {
          manualChunks: {
            vue: ['vue', 'vue-router', 'vuex', 'vue-i18n'],
            antd: ['ant-design-vue'],
            d3: ['d3', 'd3v7'],
            echarts: ['echarts', 'element-resize-detector', 'save-svg-as-png'],
            utils: [
              'axios',
              'url-template',
              'lodash',
              'moment',
              'big.js',
              'eventemitter3',
              'file-saver',
              'js-cookie',
              'html2canvas',
              'loadjs',
              'querystring',
              'socket.io-client',
              'uuid',
              'vue-typed-mixins',
              'vuedraggable',
              'vue-scrollto',
              'vue-echarts',
              'crypto-js',
            ],
          },
        },
      },
      commonjsOptions: {
        transformMixedEsModules: true,
      },
    },
    define: {
      'process.env.NODE_ENV': JSON.stringify(mode),
      'import.meta.env.APP_VERSION': JSON.stringify(APP_VERSION),
      'import.meta.env.APP_RUNTIME_ENV': JSON.stringify(APP_RUNTIME_ENV),
      'import.meta.env.APP_SENTRY_ENVIRONMENT': JSON.stringify(SENTRY_ENVIRONMENT),
      'import.meta.env.APP_SENTRY_RELEASE': JSON.stringify(SENTRY_RELEASE),
      'import.meta.env.APP_SENTRY_DSN': JSON.stringify(SENTRY_DSN),
    },
    plugins: getPlugins(mode),
    resolve: {
      alias: ALIAS,
      extensions: ['.mjs', '.js', '.mts', '.ts', '.jsx', '.tsx', '.json', '.vue'],
    },
    server: {
      host: true,
      allowedHosts: ['.greatld.com', '.qcc.com'],
      port: 8080,
      proxy: {
        '/insights': {
          target: process.env.API_ENTRYPOINT || 'http://i.test.greatld.com',
          changeOrigin: true,
          xfwd: false, // 禁止本地 node 服务器重写 x-forward-for
        },
        // '^/rover/legacy/construction': {
        //   target: 'http://j.test.greatld.com',
        //   pathRewrite: {
        //     '^/rover/legacy': '/qcc',
        //   },
        //   changeOrigin: true,
        //   xfwd: false, // 禁止本地 node 服务器重写 x-forward-for
        // },
        '/qcc/': {
          target: process.env.API_ENTRYPOINT || 'http://i.test.greatld.com',
          changeOrigin: true,
          xfwd: false, // 禁止本地 node 服务器重写 x-forward-for
        },
        '^/rover': {
          // target: process.env.API_ENTRYPOINT || 'http://local.greatld.com:7001',
          target: process.env.API_ENTRYPOINT || 'http://i.test.greatld.com',
          changeOrigin: true,
          xfwd: false, // 禁止本地 node 服务器重写 x-forward-for
        },
      },
    },
    css: {
      modules: {
        localsConvention: 'camelCaseOnly',
        generateScopedName: '[name]__[local]_[hash:base64:5]',
      },
      preprocessorOptions: {
        less: {
          javascriptEnabled: true,
          math: 'always',
        },
      },
    },
  };
});
